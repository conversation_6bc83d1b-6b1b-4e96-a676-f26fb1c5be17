Cypress.Commands.add('rejectCookies', () => {
	cy.getCookie('SKcookieConsent').then((cookie) => {
		if (cookie !== null) {
			return;
		}

		cy.get('body').then(($body) => {
			const cookieConsent = $body.find('.b-cookie');
			if (cookieConsent.length > 0) {
				cy.get('button[data-cookie-settings]').click();
				return cy.get('button[data-cookie-save]').click();;
			}
		});
	});
});

Cypress.Commands.add('login', (username, password) => {
	username ??= '<EMAIL>';
	password ??= '7357p455w0RD!';


	cy.visit('/prihlaseni');
	cy.get('.app-loaded', {timeout: 10000 });
	cy.rejectCookies();

	cy.get('.f-login input[name="username"]').last().type(username);
	cy.get('.f-login input[name="password"]').last().type(`${password}{enter}`, { log: false });

	cy.url().should('not.include', '/prihlaseni');
	cy.getCookie('PHPSESSID').should('exist');

});

Cypress.Commands.add('basketFlow', ({ voucherCode = '', price, deliveryPrice}) => {
	// click the "go to cart" button
	cy.contains('a[href="/kosik"]', 'btn_go_to_cart', {timeout: 15000 }).click();
	cy.contains('.b-cart__item', 'Skladem');


	if (voucherCode !== '') {
		cy.get('.app-loaded', {timeout: 10000 });

		cy.get('[data-action="toggle-class#toggleInp"]').click({force: true});
		cy.get('#frm-cart-form-voucher', { force: true, timeout: 10000 }).should('be.visible').type(voucherCode);
		cy.contains('button[name="send"]', 'cart_voucher_btn', { timeout: 10000 }).click();

		cy.contains('.b-voucher__voucher', 'voucher_discount: 100 Kč');
	}

	cy.contains('a[href="/objednavka-krok-1"]', 'btn_cart_continue_delivery_shipping').click();
	cy.contains('.b-cart-summary__total', price + ' Kč');
	cy.get('.app-loaded', {timeout: 10000 });

	// select delivery and payment methods
	cy.contains('.f-method__more > button', 'cart_delivery_toggle').click();
	cy.get('.f-method__item input[name="deliveryMethod"][type="radio"][value="8"]', {timeout: 10000 }).click();
	cy.get('.f-method__item input[name="paymentMethod"][type="radio"][value="6"]', {timeout: 10000 }).click();
	cy.contains('.b-cart-summary__total', price + deliveryPrice + ' Kč', {timeout: 10000 });




	// click the big green button
	cy.contains('button[name="next"]', 'btn_order_continue', {timeout: 10000 }).click();

	// fill billing address
	cy.get('input[name="email"]', {timeout: 10000 }).type('<EMAIL>');
	cy.get('input[name="inv_firstname"]').type('E2E');
	cy.get('input[name="inv_lastname"]').type('Test');
	cy.get('input[type="tel"]').first().type('728123456');
	cy.get('input[name="inv_street"]').type('Ulice 1');
	cy.get('input[name="inv_city"]').type('Mesto');
	cy.get('input[name="inv_zip"]').type('06401');

	// click the big green button
	cy.contains('button[name="next"]', 'Odeslat objednávku').click();

	// assert that order has been successfully submitted
	cy.url().should('include', '/objednavka-dokoncena');
	cy.get('.app-loaded', {timeout: 10000 });
	cy.contains('h1', 'title_order_success');

	cy.contains('.b-total__table', deliveryPrice + ' Kč', {timeout: 10000 });
	cy.contains('.b-total__table', price + deliveryPrice + ' Kč', {timeout: 10000 });

});
