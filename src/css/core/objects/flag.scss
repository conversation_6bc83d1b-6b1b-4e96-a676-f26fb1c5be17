@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.flags {
	@extend %reset-ul;
	display: flex;
	gap: 0.3rem;
	flex-direction: column;
	li {
		@extend %reset-ul-li;
		display: flex;
	}
}
.flag {
	--flag-hover-bg: oklch(from var(--flag-bg) calc(l * 0.75) c h);
	--flag-hover-color: #{variables.$color-white};
	--flag-bd: transparent;
	--flag-color: #{variables.$color-white};
	--flag-bg: #{variables.$color-link};
	--icon-size: 1.5rem;
	--gap: 0.1rem;
	display: inline-flex;
	vertical-align: middle;
	gap: var(--gap);
	align-items: center;
	min-height: 2.3rem;
	padding: 0.2rem 0.7rem 0.1rem;
	border: 0.1rem solid var(--flag-bd);
	border-radius: variables.$border-radius;
	background: var(--flag-bg);
	color: var(--flag-color);
	font-family: variables.$font-secondary;
	font-weight: 600;
	font-size: 1.1rem;
	line-height: 1;
	text-transform: uppercase;
	text-decoration: none;
	transition: color variables.$t, background-color variables.$t, border-color variables.$t;
	&__icon {
		flex: 0 0 auto;
		width: var(--icon-size);
		height: var(--icon-size);
	}

	// MODIF
	&--primary {
		--flag-bg: #{variables.$color-primary};
	}
	&--secondary {
		--flag-bg: #{variables.$color-secondary};
	}
	&--black {
		--flag-bg: #{variables.$color-black};
	}
	&--black#{&}--bd {
		--flag-bd: #{variables.$color-black};
		--flag-color: #{variables.$color-black};
	}
	&--gray {
		--flag-bg: #{variables.$color-gray};
	}
	&--gray#{&}--bd {
		--flag-bd: #{variables.$color-gray};
		--flag-color: #{variables.$color-gray};
	}
	&--red {
		--flag-bg: #{variables.$color-red};
	}
	&--green-dark {
		--flag-bg: #{variables.$color-green-dark-3};
	}
	&--sand {
		--flag-bd: #{variables.$color-sand-light-2};
		--flag-bg: #{variables.$color-sand-light-2};
		--flag-color: #{variables.$color-black};
	}
	&--bd {
		--flag-bg: transparent;
		--flag-hover-bg: var(--flag-bd);
		--flag-hover-color: #{variables.$color-white};
	}
	&--blue-light {
		--flag-bg: #{variables.$color-blue-light};
		--color-bd: transparent;
		--flag-hover-color: #{variables.$color-link};
		--flag-color: #{variables.$color-blue};
		--flag-hover-bg: #{variables.$color-blue-light-2};
	}

	@at-root a#{&} {
		.hoverevents &:hover {
			border-color: var(--flag-hover-bg);
			background: var(--flag-hover-bg);
			color: var(--flag-hover-color);
		}
	}
}
