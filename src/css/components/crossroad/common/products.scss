@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.c-products {
	margin: 0 0 4rem;
	&__list {
		--grid-x-spacing: 1rem;
		--grid-y-spacing: 3.2rem;
	}
	&__pager {
		margin: 0;
		padding: 4rem 0 0;
	}

	// MODIF
	&__list--grid &__item.grid__cell {
		width: 50%;
	}
	&__list--list {
		--grid-x-spacing: 0;
		--grid-y-spacing: 0;
	}

	// MQ
	@media (config.$sm-up) {
		&__list--grid &__item.grid__cell {
			width: 33.33%;
		}
	}
	@media (config.$md-up) {
		margin: 0 0 6.4rem;
		&__list--grid &__item.grid__cell {
			width: 25%;
		}
		&__pager {
			padding: 6.4rem 0 0;
		}
	}
	@media (config.$xxl-up) {
		&__list--grid {
			--grid-x-spacing: 5.4rem;
			--grid-y-spacing: 4.6rem;
		}
		&__list--grid &__item.grid__cell {
			width: 20%;
		}
	}
}
