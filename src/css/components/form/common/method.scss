@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-method {
	--padding-x: 1.5rem;
	--padding-x-inp: calc(var(--padding-x) + var(--gap) + 2.2rem);
	--padding-y: 1.2rem;
	--gap: 0.8rem;
	--change-height: 0rem;
	$s: &;
	&__list {
		@extend %reset-ul;
		display: grid;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		display: grid;
		grid-template-columns: subgrid;
		gap: 0.4rem var(--gap);
		align-items: center;
		margin: 0 0 0.8rem;
		padding: var(--padding-y) var(--padding-x);
		border-radius: variables.$border-radius;
		background: variables.$color-white;
		transition: box-shadow variables.$t;
		&:last-child {
			margin: 0;
		}
		&:has(input) {
			padding-left: var(--padding-x-inp);
		}
	}
	&__inp {
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		cursor: pointer;
		appearance: none;
	}
	&__label {
		display: contents;
	}
	&__inner,
	&__inner.inp-item__text {
		display: contents;
		font-size: 1.4rem;
		&::before,
		&::after {
			top: calc(50% - var(--change-height) / 2);
			left: var(--padding-x);
			transform: translateY(-50%);
			pointer-events: none;
		}
		&::after {
			left: calc(var(--padding-x) + 0.5rem);
		}
	}
	&__img {
		display: flex;
		gap: 0.5rem;
		flex-direction: column;
		width: 4rem;
		img {
			aspect-ratio: 60 / 46;
			object-fit: contain;
		}
		&--2 img {
			aspect-ratio: 60 / 30;
		}
		&--3 img {
			height: 2.2rem;
			aspect-ratio: 60 / 22;
		}
	}
	&__desc {
		color: variables.$color-gray-dark;
		font-size: 1.2rem;
		&:not(:has(*)) {
			display: none;
		}
	}
	&__delivery::first-letter {
		text-transform: capitalize;
	}
	&__price {
		text-align: right;
		text-transform: capitalize;
	}
	&__tooltip {
		margin-left: 0.8rem;
		.tooltip__content {
			max-width: 37rem;
		}
	}
	&__more {
		font-size: 1.4rem;
	}
	&__tooltip .tooltip__info,
	&__desc a,
	&__desc button,
	&__change {
		position: relative;
		z-index: 1;
	}
	&__change {
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		display: flex;
		align-items: center;
		min-height: var(--change-height);
		padding: 0 var(--padding-x);
		border-top: 0.1rem solid variables.$color-sand-light-2;
		font-size: 1.4rem;
		.item-icon {
			--icon-size: 1.5rem;
			--gap: 0.7rem;
		}
	}
	&__title {
		text-wrap: balance;
	}
	&__other {
		display: flex;
		grid-column: auto / span 4;
		gap: 1rem;
		flex-direction: column;
	}
	&__recommend {
		display: flex;
		gap: 0.4rem;
		flex-wrap: wrap;
	}

	// MODIF
	// &--paymentMethod {
	// 	#{$s}__title {
	// 		display: flex;
	// 		gap: 0.8rem;
	// 		align-items: center;
	// 	}
	// 	#{$s}__img {
	// 		flex-direction: row;
	// 		margin: 0;
	// 	}
	// 	#{$s}__tooltip {
	// 		margin: 0;
	// 	}
	// }
	&__label--other {
		display: block;
		.inp-item__text {
			display: flex;
			justify-content: space-between;
		}
	}
	&__item--pickup &__inner::before,
	&__item--pickup &__inner::after {
		top: calc(2.2rem + var(--padding-y));
	}
	&__item--group {
		& > #{$s}__label {
			cursor: default;
			pointer-events: none;
		}
		#{$s} {
			&__inner::before,
			&__inner::after {
				top: calc(1.1rem + var(--padding-y));
			}
			&__tooltip,
			&__label--other {
				cursor: pointer;
				pointer-events: auto;
			}
		}
	}

	// STATES
	&__item--pickup:not(:has(&__inp:checked)) &__desc:has(&__choose) {
		display: none;
		#{$s}__inner::before {
			top: calc(50% - var(--change-height) / 2);
		}
		#{$s}__inner::after {
			top: calc(var(--padding-x) + 0.5rem);
		}
	}
	&__item:has(&__inp:disabled) {
		background: variables.$color-sand-light-3;
		#{$s}__img {
			opacity: 0.5;
		}
		#{$s}__recommend {
			display: none;
		}
		#{$s}__price {
			grid-area: 2/2/2/5;
			text-transform: none;
		}
		#{$s}__inp {
			cursor: auto;
		}
	}
	// &--paymentMethod &__item:has(&__inp:disabled) &__price {
	// 	grid-area: 1/2/1/5;
	// }
	// tlacitko pro zmenu dopravy / platby je viditelne
	&__item.is-selected {
		--change-height: 5rem;
		padding-bottom: 7rem;
	}

	&__more:has(.u-d-n) {
		display: none;
	}
	&__item:has(&__inp:checked) {
		box-shadow: variables.$box-shadow;
	}

	// HOVERS
	.hoverevents &__item:has(input:not(:disabled)):hover {
		box-shadow: variables.$box-shadow;
	}
	.hoverevents &__label--other:hover {
		z-index: 2;
	}

	// MQ
	@supports not (grid-template-columns: subgrid) {
		&__item {
			display: flex;
			flex-wrap: wrap;
		}
		&__inner.inp-item__text {
			&::before {
				position: static;
				transform: none;
			}
		}
		&__title {
			flex: 1;
		}
		&__desc {
			width: 100%;
		}
	}
	@media (config.$md-down) {
		&__list {
			grid-template-columns: max-content 1fr max-content max-content;
		}
		&__item {
			grid-column: auto / span 4;
		}
		&__img {
			grid-area: 1/1/4/1;
		}
		&__title {
			grid-area: 1/2/1/2;
		}
		&__delivery {
			grid-area: 2/2/3/2;
			font-size: 1.2rem;
		}
		&__recommend {
			grid-area: 3/2/4/2;
			&:not(:has(*)) {
				display: none;
			}
		}
		&__price {
			grid-area: 1/4/4/4;
		}
		&__desc {
			grid-area: 4/1/4/5;
		}
		&__other {
			margin: 0.3rem 0 0 calc(var(--padding-x-inp) * -1 + var(--padding-x));
			padding-top: 1.7rem;
			border-top: 0.1rem solid variables.$color-sand-light-2;
		}

		// MODIF
		// &--paymentMethod {
		// 	#{$s} {
		// 		&__title {
		// 			grid-area: 1/1/1/2;
		// 		}
		// 		&__delivery {
		// 			display: none;
		// 		}
		// 		&__recommend {
		// 			grid-area: 2/1/3/2;
		// 		}
		// 		&__img {
		// 			grid-area: 3/1/4/2;
		// 			width: auto;
		// 			img {
		// 				width: auto;
		// 				height: 1.4rem;
		// 			}
		// 		}
		// 		&__price {
		// 			grid-area: 1/4/4/4;
		// 		}
		// 		&__desc {
		// 			grid-area: 4/1/5/5;
		// 		}
		// 	}
		// }
		// &--paymentMethod &__item--group &__desc {
		// 	grid-row: auto;
		// }
		&--static {
			#{$s} {
				&__img {
					grid-area: 1/1/1/1;
				}
				&__price {
					grid-area: 1/4/2/5;
				}
				&__free-from {
					grid-area: 2/2/3/5;
				}
			}
			#{$s}__item:has(#{$s}__free-from) #{$s}__img {
				grid-area: 1/1/3/1;
			}
		}
		// &--static#{&}--paymentMethod {
		// 	#{$s} {
		// 		&__img {
		// 			grid-area: 2/1/2/1;
		// 		}
		// 		&__price {
		// 			grid-area: 1/4/3/5;
		// 		}
		// 	}
		// }
	}
	@media (config.$md-up) {
		--padding-x: 3rem;
		--padding-y: 2rem;
		--gap: 2rem;
		&__list {
			grid-template-columns: max-content 1fr max-content auto auto;
		}
		&__item {
			grid-column: auto / span 5;
			min-height: 8.6rem;
		}
		&__img {
			width: 6rem;
		}
		&__desc {
			grid-area: 2/1/2/6;
			font-size: 1.4rem;
		}
		&__other {
			grid-column: auto / span 5;
			padding-top: 1.2rem;
		}

		// MODIF
		// &--paymentMethod {
		// 	#{$s} {
		// 		&__img {
		// 			grid-area: 1/2/1/2;
		// 			// margin-left: calc(var(--gap) * -1 + 0.8rem);
		// 			img {
		// 				width: auto;
		// 				height: 1.8rem;
		// 			}
		// 		}
		// 		&__recommended {
		// 			grid-area: 1/3/1/3;
		// 		}
		// 		&__price {
		// 			grid-area: 1/4/1/4;
		// 		}
		// 	}
		// }
		// &--static#{&}--paymentMethod {
		// 	#{$s} {
		// 		&__name {
		// 			grid-area: 1/1/1/1;
		// 		}
		// 		&__price {
		// 			grid-area: 1/3/1/4;
		// 		}
		// 	}
		// }
		&__item:has(&__inp:disabled) {
			#{$s}__price {
				grid-area: 1/3/1/6;
				text-align: right;
			}
		}
	}
	@media (config.$lg-up) {
		--padding-x: 1.5rem;
		--padding-y: 1.2rem;
		--gap: 1.5rem;
	}
	@media (config.$xl-up) {
		--padding-x: 3rem;
		--padding-y: 1.7rem;
		--gap: 2rem;
		&__inner.inp-item__text {
			font-size: 1.5rem;
		}
		&__delivery,
		&__price {
			margin-left: 3.2rem;
		}
	}
}
