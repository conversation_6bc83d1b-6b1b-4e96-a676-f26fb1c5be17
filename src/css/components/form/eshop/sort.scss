@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-sort {
	margin: 0 0 1.6rem;
	font-size: 1.4rem;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		display: flex;
		align-items: center;
		color: inherit;
	}
	&__total {
		line-height: 1.2;
	}

	// MQ
	@media (config.$lg-down) {
		display: flex;
		gap: 1rem;
		align-items: center;
		&__wrap {
			position: relative;
			flex: 0 0 20rem;
		}
		&__list {
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			z-index: 3;
		}
		&__item {
			border: 0.1rem solid variables.$color-sand-light-outline;
			border-width: 0 0.1rem;
			background: variables.$color-white;
			&:last-child {
				border-width: 0 0.1rem 0.1rem;
				border-radius: 0 0 variables.$border-radius variables.$border-radius;
			}
		}
		&__link {
			gap: 0.5rem;
			min-height: 3.6rem;
			padding: 0.5rem 1.5rem;
		}
		&__arrow {
			width: 1.5rem;
			margin: 0 0 0 auto;
			color: variables.$color-link;
			transition: transform variables.$t;
		}

		// STATES
		&__item.is-active {
			position: absolute;
			right: 0;
			bottom: 100%;
			left: 0;
			border: 0.1rem solid variables.$color-sand-light-outline;
			border-radius: variables.$border-radius;
		}
		&__item.is-active &__link {
			padding: 0.5rem 0.9rem 0.5rem 1.5rem;
		}
		&__wrap.is-open &__item.is-active {
			border-radius: variables.$border-radius variables.$border-radius 0 0;
			border-bottom-color: transparent;
		}
		&__wrap.is-open &__arrow {
			transform: scale(-1);
		}
		&__type,
		&__wrap:not(.is-open) &__item:not(.is-active) {
			display: none;
		}
	}
	@media (config.$lg-up) {
		margin: 0 0 3.2rem;
		font-size: 1.5rem;
		&__wrap {
			display: flex;
			gap: 1rem;
			justify-content: space-between;
			align-items: flex-start;
			border-bottom: 0.1rem solid variables.$color-sand-light-2;
		}
		&__list {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -1.6rem;
		}
		&__link {
			padding: 0.6rem 0.5rem 1.7rem;
			text-decoration: none;
		}
		&__total {
			color: variables.$color-sand-dark;
			font-size: 1rem;
			text-align: right;
			text-transform: uppercase;
		}
		&__arrow {
			display: none;
		}

		// STATES
		&__item.is-active &__link {
			position: relative;
			&::after {
				content: '';
				position: absolute;
				right: 1.6rem;
				bottom: -0.2rem;
				left: 1.6rem;
				height: 0.3rem;
				background: variables.$color-link;
			}
		}
	}
	@media (config.$xl-up) {
		&__link {
			padding: 0.6rem 1.6rem 1.7rem;
		}
	}
}
