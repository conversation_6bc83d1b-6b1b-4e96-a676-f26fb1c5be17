@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-review {
	&__head {
		display: flex;
		gap: 0.8rem;
		align-items: center;
		margin: 0 0 1.2rem;
		font-size: 1.4rem;
		line-height: calc(20 / 15);
	}
	&__img {
		flex: 0 0 auto;
		width: 5.2rem;
	}
	&__author {
		display: flex;
		gap: 0.2rem 0.8rem;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 0.2rem;
	}
	&__verified {
		--icon-size: 1.2rem;
		--gap: 0.4rem;
		--icon-offset: -0.1rem;
		font-size: 1.3rem;
	}
	&__stars {
		--icon-size: 1.5rem;
		--gap: 0rem;
	}
	&__content {
		font-size: 1.5rem;
		line-height: calc(20 / 16);
	}
	&__list {
		@extend %reset-ul;
		margin: 0 0 1.2rem;
		li {
			@extend %reset-ul-li;
			position: relative;
			margin: 0 0 0.8rem;
			padding-left: 2rem;
			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 0;
				width: 1.2rem;
				height: 0.2rem;
				border-radius: 0.2rem;
			}
		}
		li:last-child {
			margin: 0;
		}
	}
	&__helpful,
	&__links {
		display: flex;
		gap: 1.2rem;
		align-items: center;
		color: variables.$color-gray;
		font-size: 1.4rem;
	}
	&__like,
	&__dislike {
		--icon-size: 2rem;
		--gap: 0.5rem;
		color: inherit;
	}

	// STATES
	&__content:not(.is-open) &__text {
		@include mixins.line-clamp(4);
	}
	&__content.is-open &__more {
		display: none;
	}

	// MODIF
	&__list--positive li {
		&::before,
		&::after {
			background: variables.$color-primary;
		}
		&::after {
			transform: rotate(90deg);
		}
	}
	&__list--negative li {
		&::before,
		&::after {
			background: variables.$color-red;
		}
	}

	// MQ
	@media (config.$md-up) {
		&__head {
			font-size: 1.5rem;
			line-height: calc(23 / 15);
		}
		&__content {
			font-size: 1.6rem;
			line-height: calc(22 / 16);
		}
	}
}
