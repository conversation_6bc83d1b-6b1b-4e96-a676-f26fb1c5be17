@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-theme {
	height: 100%;
	padding: 2rem 2rem 1.7rem;
	&__imgs {
		display: flex;
		gap: 0.5rem;
		height: 11.2rem;
		margin: 0 -2rem 1.5rem 0;
	}
	&__img {
		flex: 0 0 auto;
		width: 7rem;
	}
	&__count {
		margin: 0;
		color: variables.$color-gray-dark;
		font-size: 1.3rem;
	}

	// MQ
	@media (config.$md-up) {
		padding: 3rem 3rem 2.5rem;
		&__imgs {
			height: 13.7rem;
		}
		&__img {
			width: 8.5rem;
		}
	}
}
