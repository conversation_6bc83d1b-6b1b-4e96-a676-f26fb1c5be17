@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-store {
	--box-padding: 5.2rem;
	font-size: 1.5rem;
	line-height: calc(23 / 15);
	&__title {
		margin: 0 0 1.6rem;
	}
	&__photos {
		--arrow-position: -0.7rem;
		height: 19.8rem;
		overflow: visible;
		.b-photos__helper {
			overflow: hidden;
		}
	}
	&__map {
		height: 100%;
		min-height: 35rem;
	}

	// MQ
	@media (config.$md-down) {
		&__photos .b-photos__helper {
			margin: 0 calc(var(--row-main-gutter) * -1);
			padding: 0 var(--row-main-gutter);
		}
	}
	@media (config.$md-up) {
		&__title {
			margin: 0 0 3rem;
		}
		&__photos {
			--arrow-position: -2.8rem;
			height: 56.6rem;
			min-height: 100%;
		}
		&__info,
		&__map,
		&__transport {
			height: 100%;
		}
	}
}
