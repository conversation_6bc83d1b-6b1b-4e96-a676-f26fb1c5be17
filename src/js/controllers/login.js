import { Controller } from '@hotwired/stimulus';
import { useClickOutside } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static targets = ['msg'];

		connect() {
			useClickOutside(this);
		}
		clickOutside = () => {
			setTimeout(() => {
				this.msgTarget.querySelector('.item-icon__text').innerHTML = '';
				this.msgTarget.classList.add('u-d-n');
			}, 300);
		};
	};
};
