import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		async connect() {
			const { PageFlip } = await import('page-flip');
			const images = this.element.querySelectorAll('img');

			const initializePageFlip = () => {
				const pageFlip = new PageFlip(this.element, {
					width: images[0].clientWidth,
					height: images[0].clientHeight,
					size: 'stretch',
					maxShadowOpacity: 0.5,
					showCover: false,
				});
				pageFlip.loadFromHTML(images);
				images.forEach((image) => image.classList.remove('u-vhide'));
			};

			if (images[0].complete) {
				initializePageFlip();
			} else {
				images[0].addEventListener('load', initializePageFlip);
			}
		}
	};
};
