[program:warmup]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume warmUpFront --limit=100 --time-limit=120
user={$DEPLOYMENT_USER}
numprocs=1
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d


[program:default]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume defaultFront --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=1
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:export-order]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume orderFront --limit=1 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:elastic]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume elasticPriorityFront elasticFront --limit=20 --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=15
startsecs=0
autostart=true
autorestart=true
startretries=15
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:parameter]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume parameterFront --limit=1 --time-limit=60
user={$DEPLOYMENT_USER}
numprocs=1
startsecs=0
autostart=true
autorestart=true
startretries=5
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:import-stock]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume importStockFront --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=15
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:import-customer]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume importCustomerFront --limit=10 --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=3
startsecs=0
autostart=true
autorestart=true
startretries=5
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:import-price]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume importPriceFront --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:import-product]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume importProductFront importProductImagesFront --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:import-order]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume importOrderFront --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:import-coupon]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume importCouponFront --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:failure]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume failure --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:scheduler-default]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume scheduler_default --memory-limit=128M --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=2
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:scheduler-import-stock]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume scheduler_import_stock --memory-limit=128M --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=2
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:scheduler-import]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume scheduler_import --memory-limit=256M --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=2
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:scheduler-cron-commands]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume cronCommands --limit=1
user={$DEPLOYMENT_USER}
numprocs=10
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:score]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume score --limit=50 --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=6
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d
