<?php

declare(strict_types=1);

namespace App\Event;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\State\State;
use Brick\Money\Currency;
use Symfony\Contracts\EventDispatcher\Event;

final class AddToWishlist extends Event
{

	public function __construct(
		public readonly ProductLocalization $productLocalization,
		public readonly Mutation $mutation,
		public readonly State $state,
		public readonly PriceLevel $priceLevel,
		public readonly Currency $currency,
		public readonly ?string $listId = null,
		public readonly ?string $listName = null,
	)
	{
	}

}
