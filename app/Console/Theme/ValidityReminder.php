<?php declare(strict_types = 1);

namespace App\Console\Theme;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\PostType\Theme\Model\Orm\ThemeLocalization\ThemeLocalizationModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'theme:reminder',
	description: 'Remind themes tahts going to expire eithin 14 days',
)]
#[AsCronTask('# #(10-14) * * *', arguments: 'cs', transports: 'cronCommands')]
class ValidityReminder extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
		private readonly ThemeLocalizationModel $themeLocalizationModel
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED)
			->setDescription('Remind themes tahts going to expire eithin 14 days');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$mutation = $this->orm->mutation->getByCode($input->getArgument('mutationCode'));
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$this->themeLocalizationModel->remindExpiration($mutation);

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
