<?php

declare(strict_types=1);

namespace App\Console\Erp;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductLocalization\ProductLocalizationModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'erp:product:damaged',
)]
#[AsCronTask(expression: '# #(3-6) * * *', schedule: 'import', transports: 'cronCommands')]
final class ProductDamagedCommand extends \App\Console\BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly Facade $facade,
		private readonly ProductLocalizationModel $productLocalizationModel,
	)
	{
		parent::__construct();
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->mutationHolder->setMutation($this->mutationsHolder->getDefault());
		$this->orm->setMutation($this->mutationsHolder->getDefault());
		$this->orm->setPublicOnly(false);

		$i = 0;
		foreach ($this->orm->productLocalization->findBy(['product->isDamaged' => true, 'public' => 1]) as $productLocalization) {
			$stockCount = $productLocalization->product->totalSupplyCount;
			$this->productLocalizationModel->handleDamaged($productLocalization, $stockCount);
			$this->facade->updateOrDeleteAllMutations($productLocalization->product);
			$i++;
		}

		$this->orm->flush();

		$output->writeLn('DONE (' . $i . ')');

		return $this->end(self::SUCCESS);
	}

}
