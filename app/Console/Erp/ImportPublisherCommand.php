<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\PublisherConnector;
use App\Model\Erp\Importer\PublisherImporter;
use App\Model\Erp\Importer\PublisherImporterFactory;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'erp:import:publisher',
	description: 'Publisher import',
)]
#[AsCronTask(expression: '# #(1-2) * * *', schedule: 'import', transports: 'cronCommands')]
final class ImportPublisherCommand extends BaseCommand
{

	private PublisherImporter $importer;

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);
		$this->importer = $this->importers->get(PublisherImporterFactory::class)->create($this->connectors->get(PublisherConnector::class));
		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof PublisherImporter);
		return $this->importer->import(
			ImportCache::TYPE_PUBLISHER
		);
	}

	protected function processResult(Result $result): void
	{
		assert($this->importer instanceof PublisherImporter);
		$this->importer->afterImport($result);
	}

}
