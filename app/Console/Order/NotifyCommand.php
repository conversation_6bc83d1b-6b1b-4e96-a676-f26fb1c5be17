<?php declare(strict_types = 1);

namespace App\Console\Order;

use App\Console\BaseCommand;
use App\Infrastructure\Latte\Filters;
use App\Model\Email\CommonFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use Nextras\Orm\Collection\ICollection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\ConsoleOutputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'order:notify',
	description: 'E-mail notification send for unsynced orders',
)]
##[AsCronTask('# #(2-3) * * *', transports: 'cronCommands')]
class NotifyCommand extends BaseCommand
{
	protected Mutation $mutation;
	public function __construct(
		protected readonly Orm $orm,
		protected readonly CommonFactory $commonFactory,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly TranslatorDB $translator,
		private readonly LinkFactory $linkFactory,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addOption('email', null, InputOption::VALUE_REQUIRED, 'E-mail address');
		$this->addOption('limit', null, InputOption::VALUE_REQUIRED, 'Limit', 10);
		parent::configure(); // TODO: Change the autogenerated stub
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$io = new SymfonyStyle($input, $output instanceof ConsoleOutputInterface ? $output->getErrorOutput() : $output);
		$this->start($input);

		$this->mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);

		Filters::$mutation = $this->mutation;
		Filters::$translator = $this->translator;


		$email = $input->getOption('email');
		$limit = $input->getOption('limit');
		$orders = [];
		if ($email !== null) {
			$io->title('Finding last order for email: ' . $email);
			$orders = $this->orm->order->findBy(['email' => $email])->orderBy('id', ICollection::DESC)->limitBy(1)->fetchPairs('orderNumber');

			if (count($orders) === 0) {
				$io->error('No orders found for email: ' . $email);
				return $this->end(self::FAILURE);
			}

			$io->comment('Found <comment>' . array_key_first($orders) . '</comment> order...');
		} else {
			$collection = $this->orm->order->findBy(['state' => OrderState::Placed, 'syncedAt' => null, 'notified' => false]);
			$io->comment('Found <comment>' . $collection->countStored() . '</comment> orders...');
			$orders = $collection->limitBy($limit)->fetchPairs('orderNumber');

		}


		/** @var Order $order */
		foreach ($orders as $order) {
			$io->text('Sending email for order: ' . $order->orderNumber);
			$this->commonFactory->create()->send(
				'',
				$order->email,
				'order',
				[
					'order' => $order,
					'ORDER_URL' => $this->linkFactory->linkTranslateToNette($this->mutation->pages->step3, ['orderHash' => $order->hash, 'orderId' => $order->id]),
					'NUMBER' => $order->orderNumber,
				]
			);
		}







		$io->success('DONE');

		return $this->end(self::SUCCESS);
	}

}
