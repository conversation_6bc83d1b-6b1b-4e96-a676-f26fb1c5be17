<?php declare(strict_types = 1);

namespace App\Console\Order;

use App\Console\BaseCommand;
use App\Infrastructure\Latte\Filters;
use App\Model\Email\CommonFactory;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use Nextras\Orm\Collection\ICollection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\ConsoleOutputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
	name: 'order:notify',
	description: 'Send backup e-mail notifications for orders that failed to sync within 5 minutes',
)]
// [AsCronTask('# #(2-3) * * *', transports: 'cronCommands')]
class NotifyCommand extends BaseCommand
{

	protected Mutation $mutation;

	public function __construct(
		protected readonly Orm $orm,
		protected readonly CommonFactory $commonFactory,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly TranslatorDB $translator,
		private readonly LinkFactory $linkFactory,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addOption('email', null, InputOption::VALUE_REQUIRED, 'E-mail address');
		$this->addOption('limit', null, InputOption::VALUE_REQUIRED, 'Limit', 10);
		$this->addOption('interval', null, InputOption::VALUE_REQUIRED, 'Time interval in minutes to wait before sending backup email', 5);
		$this->addOption('dry-run', null, InputOption::VALUE_NONE, 'Show what would be sent without actually sending emails');
		parent::configure();
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$io = new SymfonyStyle($input, $output instanceof ConsoleOutputInterface ? $output->getErrorOutput() : $output);
		$this->start($input);

		$this->mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);

		Filters::$mutation = $this->mutation;
		Filters::$translator = $this->translator;

		$email = $input->getOption('email');
		$limit = $input->getOption('limit');
		$interval = (int) $input->getOption('interval');
		$isDryRun = $input->getOption('dry-run');

		if ($isDryRun) {
			$io->note('DRY RUN MODE: No emails will be sent');
		}
		if ($email !== null) {
			$io->title('Finding last order for email: ' . $email);
			$orders = $this->orm->order->findBy(['email' => $email])->orderBy('id', ICollection::DESC)->limitBy(1)->fetchPairs('orderNumber');

			if (count($orders) === 0) {
				$io->error('No orders found for email: ' . $email);
				return $this->end(self::FAILURE);
			}

			$io->comment('Found <comment>' . array_key_first($orders) . '</comment> order...');
		} else {
			$intervalAgo = new \DateTimeImmutable('-' . $interval . ' minutes');
			$collection = $this->orm->order->findBy([
				'state' => OrderState::Placed,
				'syncedAt' => null,
				'notified' => false,
				'placedAt<=' => $intervalAgo,
			]);
			$io->comment('Found <comment>' . $collection->countStored() . '</comment> orders older than ' . $interval . ' minutes...');
			$orders = $collection->limitBy($limit)->fetchPairs('orderNumber');

		}

		/** @var Order $order */
		foreach ($orders as $order) {
			if ($isDryRun) {
				$io->text('Would send email for order: ' . $order->orderNumber . ' to: ' . $order->email);
			} else {
				$io->text('Sending email for order: ' . $order->orderNumber);

				try {
					$this->orm->getConnection()->transactional(function () use ($order) {
						$this->commonFactory->create()->send(
							'',
							$order->email,
							'order',
							[
								'order'     => $order,
								'ORDER_URL' => $this->linkFactory->linkTranslateToNette($this->mutation->pages->step3, ['orderHash' => $order->hash, 'orderId' => $order->id]),
								'NUMBER'    => $order->orderNumber,
							]
						);
						$order->notified = true;
						$this->orm->persistAndFlush($order);
					});
				} catch (\Throwable $e) {
					$io->error('Error sending email for order: ' . $order->orderNumber . ' to: ' . $order->email . ' error message: ' . $e->getMessage());
				}

			}
		}

		if ($isDryRun) {
			$io->success('DRY RUN COMPLETED - No emails were sent');
		} else {
			$io->success('DONE - ' . count($orders) . ' emails sent');
		}

		return $this->end(self::SUCCESS);
	}

}
