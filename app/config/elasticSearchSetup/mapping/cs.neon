
all:
	properties:
		kind:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		type:
			type: "keyword" #type: keyword - <PERSON><PERSON><PERSON><PERSON><PERSON>, jde podle toho i delat agegace
		nameSort:
			type: "text"
			fielddata: true
		name:
			type: "text"
		filter:
			properties:
				publishDate:
					type: date
				availability:
					type: keyword
					#fielddata: true

common:
	properties:
		name:
			type: "text"
		fulltext-name:
			type: "keyword"
			fields:
				baseEdgeNgram:
					type: text
					analyzer: 'baseEdgeNgram'
				baseEdgeNgramAscii:
					type: text
					analyzer: 'baseEdgeNgramAscii'
		annotation:
			type: "text"
		type:
			type: "keyword"
		lastNameFirstLetter:
			type: "keyword"
		url:
			type: "keyword"
		content:
			type: "text"
			fields:
				baseEdgeNgram:
					type: text
					analyzer: 'baseEdgeNgram'
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"



product:
	properties:
		nameSort:
			type: "keyword"
		name:
			type: "text"
			fielddata: true
		content:
			type: "text"
		annotation:
			type: "text"
		publicFrom:
			type: "date"
			format: "basic_date_time_no_millis"
		publicTo:
			type: "date"
			format: "basic_date_time_no_millis"
		dateCreated:
			type: "date"
			format: "basic_date_time_no_millis"
		datePublished:
			type: "date"
			format: "basic_date_time_no_millis"
		writersWithInfo:
			type: nested
		categoriesWithInfo:
			type: nested
		themesWithInfo:
			type: nested
		series:
			type: nested
		customFeed:
			enabled: false
		customFeedAvailability:
			enabled: false
		6MonthSale:   # add for order
			type: integer
		score:
			type: float

		fulltext-name:
			type: "keyword"
			fields:
				baseEdgeNgram:
					type: text
					analyzer: 'baseEdgeNgram'
				baseEdgeNgramAscii:
					type: text
					analyzer: 'baseEdgeNgramAscii'


#		fulltext-content:
#			type: "text"
#			fielddata: true
#			fields:
#				baseEdgeNgram:
#					type: text
#					analyzer: 'baseEdgeNgram'
#				baseEdgeNgramAscii:
#					type: text
#					analyzer: 'baseEdgeNgramAscii'
		fulltext-writers:
			type: "nested"
			properties:
				name:
					type: "keyword"
					fields:
						baseEdgeNgram:
							type: text
							analyzer: 'baseEdgeNgram'
						baseEdgeNgramAscii:
							type: text
							analyzer: 'baseEdgeNgramAscii'
		fulltext-publisher:
			type: "keyword"
			fields:
				baseEdgeNgram:
					type: text
					analyzer: 'baseEdgeNgram'
				baseEdgeNgramAscii:
					type: text
					analyzer: 'baseEdgeNgramAscii'
		fulltext-categories:
			type: "text"
#			fielddata: true
#			fields:
#				baseEdgeNgram:
#					type: text
#					analyzer: 'baseEdgeNgram'
#				baseEdgeNgramAscii:
#					type: text
#					analyzer: 'baseEdgeNgramAscii'
		fulltext-themes:
			type: "keyword"
#			fielddata: true
#			fields:
#				baseEdgeNgram:
#					type: text
#					analyzer: 'baseEdgeNgram'
#				baseEdgeNgramAscii:
#					type: text
#					analyzer: 'baseEdgeNgramAscii'
		fulltext-seriesName:
			type: "keyword"
#			fielddata: true
#			fields:
#				baseEdgeNgram:
#					type: text
#					analyzer: 'baseEdgeNgram'
#				baseEdgeNgramAscii:
#					type: text
#					analyzer: 'baseEdgeNgramAscii'
		fulltext-tags:
			type: "keyword"
		fulltext-isbn:
			type: "keyword"
		fulltext-ean:
			type: "keyword"
