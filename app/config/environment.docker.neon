includes:
	- environment.dev.neon

parameters:
	stageName: local

	config:
		domainUrl: https://dobreknihy.superkoders.test/
		mutations:
			cs:
				domain: dobreknihy.superkoders.test
		elastica:
			config:
				host: dobreknihy_es
		exponea:
			projectToken: '2329a4be-5983-11ef-805c-7e19249e19c0'
			publicKey: 'vskufupxt40y9y980ddtq4uk7kh8xb2t5g2po3xrloc0wh3a2o1lficyx89gtf67'
			privateKey: 'y10rh2j273fs94plj0ksok2lykpi4r8mob1oe13ja69q095kcwwnelx9l1v14j7u'
	amqp:
		host: rabbitmq
		vhost: null

	database:
		host: dobreknihy_db
		database: dobreknihy
		user: dobreknihy
		password: root

	redis:
		host: dobreknihy_redis

http:
	proxy:
		- *********/8
		- ***********/16

#session:
#	autoStart: true

mail:
	host: dobreknihy_mailcatcher
	port: 1025
