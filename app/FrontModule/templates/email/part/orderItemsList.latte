{varType App\Model\Orm\Order\Order $order}
<h2><PERSON><PERSON><PERSON> obje<PERSON></h2>
<table style="font-size: 12px;">
	<thead>
	<tr>
		<th style="vertical-align: bottom; padding: 0 5px 0 0;"><PERSON><PERSON><PERSON></th>
		<th style="vertical-align: bottom; padding: 0 5px;">Množství</th>
		<th style="vertical-align: bottom; padding: 0 5px; width: 100px;">Cena / jedn. DPH</th>
		<th style="vertical-align: bottom; padding: 0 5px;">Sazba DPH</th>
		<th style="vertical-align: bottom; padding: 0 0 0 5px; width: 70px;">Cena s DPH</th>
	</tr>
	</thead>

	<tbody>
	{foreach $order->getItems() as $item}
		<tr>
			<td style="padding: 0 5px 0 0;">{$item->getName()}</td>
			<td style="padding: 0 5px;">{$item->amount}</td>
			<td style="padding: 0 5px;">{$item->unitPriceVat|money}</td>
			<td style="padding: 0 5px;"><span n:if="$item->vatRateValue !== null">{$item->vatRateValue->toInt()}%</span></td>
			<td style="padding: 0 0 0 5px;">{$item->totalPriceVat|money}</td>
		</tr>
	{/foreach}
	<tr>
		<td style="padding: 0 5px 0 0;" colspan="2"><strong>Doprava:</strong> {$order->delivery->getName()}</td>
		<td style="padding: 0 5px;">{$order->delivery->totalPrice|money}</td>
		<td style="padding: 0 5px;"><span n:if="$item->vatRateValue !== null">{$item->vatRateValue->toInt()}%</span></td>
		<td style="padding: 0 0 0 5px;">{$order->delivery->totalPriceVat|money}</td>
	</tr>
	<tr>
		<td style="padding: 0 5px 0 0;" colspan="2"><strong>Platba:</strong> {$order->payment->getName()}</td>
		<td style="padding: 0 5px;">{$order->payment->totalPrice|money}</td>
		<td style="padding: 0 5px;">{$order->payment->vatRate->value} <span n:if="$order->payment->vatRateValue !== null">({$order->payment->vatRateValue->toInt()}%)</span></td>
		<td style="padding: 0 0 0 5px;">{$order->payment->totalPriceVat|money}</td>
	</tr>
	</tbody>
</table>
