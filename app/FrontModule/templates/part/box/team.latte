{default $class = 'u-mb-md u-mb-xl@md'}
{default $cf = $object->cf->team ?? false}

<div n:if="$cf" n:class="b-team, $class">
	<h2 n:if="$cf->title ?? false" class="b-team__title">
		{$cf->title}
	</h2>
	<ul n:if="count($cf->items ?? [])" class="b-team__grid grid">
		{foreach $cf->items as $item}
			{if $item->person ?? false}
				{php $image = isset($item->person->image) ? $item->person->image->getEntity() ?? false : false}
				<li n:if="$item->person->name ?? false" class="grid__cell size--6-12@sm size--auto@md">
					<p class="b-team__inner u-mb-0">
						<span class="b-team__img img img--circle">
							{if $image}
								<img src="{$image->getSize('sm')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
							{else}
								<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
							{/if}
						</span>
						<span class="b-team__content">
							<b class="b-team__name">
								{$item->person->name}
							</b>
							<span n:if="$item->person->position ?? false" class="b-team__position">
								{$item->person->position}
							</span>
						</span>
					</p>
				</li>
			{/if}
		{/foreach}
	</ul>
</div>