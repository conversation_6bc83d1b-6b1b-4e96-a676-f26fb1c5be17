{default $class = 'u-mb-0'}
{default $total = false}
{default $current = false}
{default $delta = false}
{default $isFree = false}

<p n:if="$total && $current && $delta" n:class="b-delivery, $class">
	{if $hasItemWithFreeDelivery || $hasUserFreeDelivery || $hasFreeDeliveryVoucher}
		{* Zdarma *}
		<span class="b-delivery__text item-icon u-fw-b u-c-green">
			{('car')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{_"free_delivery_text_free"}
			</span>
		</span>
	{elseif $isFree}
		<span class="b-delivery__text item-icon u-fw-b">
			{('car')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{capture $amount}{$delta|money}{/capture}
				{_"free_delivery_text"|replace:'%amount',$amount->__toString()}
			</span>
		</span>
		{include '../core/progress.latte', class=>'b-delivery__progress', percentage=>min($current->getAmount()->toFloat() * 100 / $total->getAmount()->toFloat(), 100)}
	{else}
		{* Zdarma *}
		<span class="b-delivery__text item-icon u-fw-b u-c-green">
			{('car')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{_"free_delivery_text_free"}
			</span>
		</span>
	{/if}
</p>
