{varType App\Model\Orm\ProductLocalization\ProductLocalization $object}
{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}

{default $class = false}
{default $showStoreText = true}

{*
{dump $object->isInStock} // bool skladem celkove
{dump $object->isInStockDefault} // bool je skladem na centralnim skladu
{dump $object->isInStockSupplier} // bool je skladem u dodavatele
{dump $object->suppliesByStock} // sklady podle ID
{dump $object->suppliesByStockAlias} // sklady podle aliasu
{dump $object->totalSupplyCount} // abs pocet na sklade celkem
{dump $object->suplyCountStockDefault} // abs pocet na defaultnim sklade
{dump $object->suplyCountStockSupplier} // abs pocet u dodavatele
*}

{*
{productAvailability->getType() === App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_PREORDER}

TYPE_ON_STOCK = 'onstock'; // Skladem na prodejne
TYPE_ON_STOCK_SUPPLIER = 'onstock_supplier'; // Skladem do x dnu;
TYPE_TO_ORDER = 'to_order'; // Na objednavku
TYPE_PREORDER = 'preorder'; // Predobjednávky
TYPE_OUT_OF_STOCK = 'out_of_stock'; // Dočasne vyprodano
TYPE_NOT_FOR_SALE = 'not_for_sale'; // Trvale vyprodáno
*}

{php $type = $productDto->productAvailabilityType}
{if $type == 'preorder'}
	{php $class .= ' availability--prepare'}
{elseif in_array($type, ['onstock', 'onstock_supplier'])}
	{php $class .= ' availability--available'}
{else}
	{php $class .= ' availability--unavailable'}
{/if}

{php $deliveryText = $productDto->productAvailabilityDeliveryText}
{php $storeText = $productDto->productAvailabilityStoreText}

<p n:if="!$productDto->isElectronic && ($deliveryText || $storeText)" n:ifcontent n:class="availability, availability--delivery, $class">
	{if $deliveryText ?? false}
		{translate($deliveryText)}
	{/if}
	{if $storeText && $showStoreText}
		{translate($storeText)|noescape}
		{* {embed $templates.'/part/core/tooltip.latte', btnClass: 'as-link', placement: 'bottom-start', ajaxContentUrl: 'https://html-mock.fly.dev/tag/table?class=table%20table-bordered'}
			{block btn}
				{$storeText|noescape}
			{/block}
		{/embed} *}
	{/if}
</p>

		{* <span>showCart: <strong>{$variant->productAvailability->isShowAddCart() ? 'yes' : 'no'}</strong></span><br>
		<span>showWatchdog: <strong>{$variant->productAvailability->isShowWatchdog() ? 'yes' : 'no'}</strong></span><br>
		<span>showSimilar: <strong>{$variant->productAvailability->isShowSimilar() ? 'yes' : 'no'}</strong></span><br> *}
