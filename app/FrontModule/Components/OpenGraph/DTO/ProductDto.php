<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use DateTimeInterface;
use Nette\Utils\DateTime;

/**
 * @method ProductLocalization getData()
 */
final class ProductDto extends OpenGraphDto implements OpenGraphDtoInterface
{

	public function __construct(
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly LinkFactory $linkFactory,
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{
	}

	final public function getLanguages(): array
	{
		$languages = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_LANGUAGE);
		$texts = [];
		foreach ($languages as $language) {
			$texts[] = $language->internalValue;
		}

		return $texts;
	}

	final public function getBind(): string|null
	{
		$bindParameter = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_BIND)->fetch();

		return $bindParameter?->internalValue ?? null;
	}

	final public function getPublishDate(): string|null
	{
		$publishDate = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_PUBLISH_DATE)->fetch();
		if ($publishDate === null) {
			return null;
		}

		try {
			$publishDate = $publishDate->internalAlias;
			$publishDateTime = DateTime::from($publishDate);
			return $publishDateTime->format(DateTimeInterface::ATOM);
		} catch (\Throwable $e) {
			// do nothing bad date
		}
		return null;
	}

	final public function getIsbn(): string|null
	{
		return $this->getData()->product->firstVariant->isbn ?? null;
	}

	final public function getWriters(): array
	{
		$writers = [];

		foreach ($this->getData()->product->writers ?? [] as $writerLocalization) {
			$writers[] = $this->linkFactory->linkTranslateToNette($writerLocalization, ['mutation' => $writerLocalization->mutation]);
		}

		return $writers;
	}

	public function getTitle(): string
	{
		return $this->getData()->getNameTitle();
	}

	public function getType(): string
	{
		$product = $this->getData()->product;

		if (($product->isAudioBook) || ($product->isBook) || ($product->isElectonicBook)) {
			return 'book';
		}

		return 'product';
	}

	public function getUrl(): string
	{
		return $this->linkFactory->linkTranslateToNette($this->getData(), ['mutation' => $this->getData()->mutation]);
	}

	public function getDescription(): string|null
	{
		return $this->getData()->description;
	}

	public function getImage(): string|null
	{
		if ($this->getProductCacheDto()->firstImageObjectMd !== null) {
			return $this->getData()->mutation->getBaseUrlWithPrefix() . $this->getProductCacheDto()->firstImageObjectMd->src;
		}

		if (!isset($this->getData()->product->firstImage->url)) {
			return null;
		}

		return $this->getData()->mutation->getBaseUrlWithPrefix() . $this->getData()->product->firstImage->url;
	}

	public function getImageWidth(): int|null
	{
		if ($this->getProductCacheDto()->firstImageObjectMd !== null) {
			return $this->getProductCacheDto()->firstImageObjectMd->width;
		}

		if (!$image = $this->getData()->product->firstImage) {
			return null;
		}

		return $image->libraryImage->getSize('md')->width ?? null;
	}

	public function getImageHeight(): int|null
	{
		if ($this->getProductCacheDto()->firstImageObjectMd !== null) {
			return $this->getProductCacheDto()->firstImageObjectMd->height;
		}
		if (!$image = $this->getData()->product->firstImage) {
			return null;
		}

		return $image->libraryImage->getSize('md')->height ?? null;
	}

	public function getImageAlt(): string|null
	{
		return $this->getProductCacheDto()->firstImageAlt ?? $this->getData()->product->firstImage?->getAlt($this->getData()->mutation) ?? null;
	}

	private function getProductCacheDto(): \App\Model\DTO\Product\ProductDto
	{
		return $this->productDtoProvider->get($this->getData()->product);
	}

}
