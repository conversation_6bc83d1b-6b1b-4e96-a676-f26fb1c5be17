{varType Nextras\Orm\Collection\ICollection $items}
{varType App\Model\ShoppingCart\ShoppingCartInterface $shoppingCart}
{snippet cartContent}
	{if $products->count() > 0}
		{define #btns}
			{default $class = false}

			<p n:class="f-btns, $class">
				{* Předchozí stránka nebo e-shop *}
				{php $previousPage = isset($_SERVER['HTTP_REFERER']) && str_contains($_SERVER['HTTP_REFERER'], $mutation->getBaseUrl())}
				{if $previousPage && !in_array($previousPage, ['/doprava-a-platba', '/dodaci-udaje'])}
					<a href="{$_SERVER['HTTP_REFERER']}" class="item-icon">
						{('arrow-left-bold')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"btn_back_shopping"}
						</span>
					</a>
				{elseif isset($pages->eshop)}
					<a href="{plink $pages->eshop}" class="item-icon">
						{('arrow-left-bold')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"btn_back_shopping"}
						</span>
					</a>
				{/if}
				<a n:if="!$shoppingCart->isDivide()" href="{plink $pages->step1}" class="btn btn--md" data-steps-target="btn">
					<span class="btn__text">
						{_"btn_cart_continue_delivery_shipping"}
					</span>
				</a>
				<a n:if="$shoppingCart->isDivide()" href="{plink $pages->popupDivideOrder}" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--content" class="btn btn--md" data-steps-target="btn">
					<span class="btn__text">
						{_"btn_cart_continue_delivery_shipping"}
					</span>
				</a>
			</p>
		{/define}

		<h1 class="u-vhide">{$object->name}</h1>
		{include $templates.'/part/box/steps.latte', currentStep: 1}

		<div n:if="$flashes !== []">
			<p n:foreach="$flashes as $flash" class="message message--{$flash->type}">{$flash->message}</p>
		</div>

		{include $templates.'/part/form/basket.latte', class: 'u-mb-sm', showVoucherInput: $showVoucherInput}


		{* Delší doba dodání (rozdělení objednávky) *}
		{* <p n:if="$shoppingCart->hasExtendedDelivery(App\FrontModule\Components\CartDivideOrder\CartDivideOrder::DAYS) && ($info = $shoppingCart->getExtendedDeliveryInfo()) !== null" class="u-maw-4-12 u-ml-auto u-mb-sm">
			<span class="tooltip tooltip--error tooltip--static top end">
				<span class="tooltip__content" data-controller="inp-change" data-inp-change-handle-value="{link divideOrder!, state: 'STATE'}" data-inp-change-replace-value="STATE">
					{var $extendedDeliveryDate = $info['extendedDelivery']['from']->format('j. n')}
					{if $info['extendedDelivery']['to'] !== null}
						{php $extendedDeliveryDate .= ' - ' . $info['extendedDelivery']['to']->format('j. n')}
					{/if}
					{_"order_msg_prolonged"|replace:'%date', $extendedDeliveryDate}
					{if $shoppingCart->getTotalProducts() > 1}
						<span class="u-d-b u-pt-xxs">
							<label class="inp-item inp-item--checkbox u-c-text">
								<input type="checkbox" class="inp-item__inp" {if $shoppingCart->isDivide()}checked{/if} value="1" data-inp-change-target="input" data-action="change->inp-change#check">
								<span class="inp-item__text">
									{_"order_msg_prolonged_divide_order"}
								</span>
							</label>
						</span>
					{/if}
					<span class="tooltip__arrow"></span>
				</span>
			</span>
		</p> *}

		<div n:snippet="btns">
			{include #btns, class: 'u-mb-xs u-mb-xl@md'}
		</div>

		{if $marketingConsent}
			<div n:ifcontent class="b-basket-products u-bgc-white u-mb-last-0 u-mb-xs u-mb-sm@md">
				{php $control['productListCustomerAlsoBuy']->setTemplateParameters(['class' => 'section--products section--w-line u-mb-md u-mb-lg@md'])}
				{control productListCustomerAlsoBuy}

				{php $control['productListRecommend']->setTemplateParameters(['class' => 'section--products section--w-line u-mb-md u-mb-lg@md'])}
				{control productListRecommend}
			</div>

			<div n:snippet="btnsConsent">
				{include #btns, class: 'u-mb-0'}
			</div>
		{/if}

	{else} {* Prázdný košík *}
		{php $cf = $object->cf->emptyBasket ?? false}

		<div n:if="$cf" class="u-maw-6-12 u-mx-auto u-pt-lg u-pt-xl@md u-mb-xl u-mb-2xl@md">
			<div class="u-ta-c u-mb-last-0 u-mb-sm">
				<h1 n:if="$cf->title ?? false" class="u-mb-xxs">
					{$cf->title}
				</h1>
				<p n:if="$cf->annot ?? false">
					{$cf->annot}
				</p>
			</div>
			<div n:if="$flashes !== []">
				<p n:foreach="$flashes as $flash" class="message message--{$flash->type}">{$flash->message}</p>
			</div>
			{include $templates.'/part/box/tips.latte', cf=>$cf}
		</div>
	{/if}
{/snippet}
