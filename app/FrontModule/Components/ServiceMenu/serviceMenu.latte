<nav class="header__service m-service" n:if="$menu">
	<ul class="m-service__list">
		{foreach $menu as $item}
			{php $link = $item->page->link}
			{php $type = $link->toggle}

			<li n:if="$type">
				{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
				{php $hrefName = ($link->systemHref??->hrefName ?? false) ?: ($link->customHref??->hrefName ?? false)}
				{php $href = $link->customHref??->href ?? false}


				{if $type == 'systemHref' && $page}
					{php $isMagazine = $page->uid == 'blog'}

					<a href="{plink $page}" n:class="header-link, $isMagazine ? item-icon, $item->selected ? is-active">
						{if $isMagazine}
							{('magazine')|icon, 'item-icon__icon'}
						{/if}
						<span n:tag-if="$isMagazine" class="item-icon__text">
							{if $hrefName}
								{$hrefName}
							{else}
								{$page->nameAnchor}
							{/if}
						</span>
					</a>
				{elseif $type == 'customHref' && $href && $hrefName}
					<a href="{$href}" class="header-link" target="_blank" rel="noopener noreferrer">
						{$hrefName}
					</a>
				{/if}
			</li>
		{/foreach}
	</ul>
</nav>

