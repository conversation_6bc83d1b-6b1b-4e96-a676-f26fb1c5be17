<?php declare(strict_types = 1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace App\FrontModule\Components\RegistrationForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\FacebookLogin\FacebookLogin;
use App\FrontModule\Components\FacebookLogin\FacebookLoginFactory;
use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\FrontModule\Components\SeznamLogin\SeznamLogin;
use App\FrontModule\Components\SeznamLogin\SeznamLoginFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class RegistrationForm extends UI\Control
{

	private Mutation $mutation;

	public function __construct(
		private readonly Tree $object,
		private readonly UserModel $userModel,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly FacebookLoginFactory $facebookLoginFactory,
		private readonly GoogleLoginFactory $googleLoginFactory,
		private readonly SeznamLoginFactory $seznamLoginFactory,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/registrationForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'form_label_email')
			->setRequired('E-mail is required');
		$form->addPassword('password', 'form_label_password')
			->setRequired();
		$form->addCheckbox('agree')->setRequired();
		$form->addCheckbox('isNewsletter', $this->translator->translate('form_label_newsletter'))->setTranslator(null)
			->setDefaultValue(1);

		$form->addSubmit('save', 'btnRegister');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'editFormValidate'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editFormValidate(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->userModel->getByEmail($values->email, $this->mutation);

		if ($user) {
			$link1 = $this->presenter->link($this->mutation->pages->userLogin, ['email' => $values->email]);
			$link2 = $this->presenter->link($this->mutation->pages->lostPassword, ['email' => $values->email]);
			$strTranslated = $this->translator->translate('mail_exist_register');

			if (strpos($strTranslated, '%link1%') !== false) {
				$strTranslated = str_replace('%link1%', $link1, $strTranslated);
			}

			if (strpos($strTranslated, '%link2%') !== false) {
				$strTranslated = str_replace('%link2%', $link2, $strTranslated);
			}

			$form->addError($strTranslated, false);
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		assert(is_array($valuesAll));
		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($this->mutation);

		$this->userModel->save($user, $valuesAll);

		try {
			$this->presenter->getUser()->login($valuesAll['email'], $valuesAll['password']);
			$this->presenter->getHttpResponse()->setCookie('registrationComplete', '1', '+1 minute');

			$this->flashMessage('form_register_ok', 'ok');
			$this->presenter->redirect($this->mutation->pages->userSection);

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		return $this->googleLoginFactory->create($this->mutation);
	}

	protected function createComponentFacebookLogin(): FacebookLogin
	{
		return $this->facebookLoginFactory->create($this->mutation);
	}

	protected function createComponentSeznamLogin(): SeznamLogin
	{
		return $this->seznamLoginFactory->create($this->mutation);
	}

}
