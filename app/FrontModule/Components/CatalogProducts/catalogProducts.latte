{varType App\FrontModule\Components\CatalogProducts\CatalogProducts $control}
{default $class = false}
{default $pager = true}
{default $title = 'h2'}
{default $products = false}
{default $ajaxPage = false}
{default $cleanFilterParam = []}
{default $isInSeries = null}

<section n:class="c-products, $class"
		{if isset($gtmItemEvent)}
	data-controller="gtm-viewport{*if $products} intersection{/if*}"
	data-gtm-viewport-script-id-value="{$gtmScriptId}"
	data-gtm-viewport-threshold-value="{$gtmThreshold}"
	data-gtm-viewport-item-class-value="b-product"
	data-gtm-viewport-item-event-value="{$gtmItemEvent}"
{/if}
>
	{if $products->count() > 0}
		<div n:class="c-products__list, $control->isViewListMode() ? c-products__list--list : c-products__list--grid, grid" n:snippet="productList" data-ajax-append>
			{default $isInSeries = null}
			<div n:foreach="$products as $key=>$product" class="c-products__item grid__cell">
				{control productBox.'-'.$product->id}
			</div>
		</div>

		{if $pager}
			{snippet productsPagerBottom}
				{control pager, [filter => $cleanFilterParam, showMoreBtn=>$showMoreBtn, class=>'c-products__pager', ajaxPage=>$ajaxPage, najaScroll=>'.c-products__list']}
			{/snippet}
		{/if}
	{elseif $cleanFilterParam !== []}
		{capture $link}{plink 'this', 'filter' => null, 'page' => null}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}
		{* TODO kelly *}
		<p class="message message--warning u-ta-c u-mb-0">
			{_message_empty_filter}
			<a href="{$link}" class="" data-naja data-naja-loader="body">
				{_btn_filter_remove}
			</a>
		</p>
	{else}
		{php $link = $presenter->getReferer()}
		{* TODO kelly *}
		<p class="message message--warning u-ta-c u-mb-0">
			{_message_empty_category}
			<a href="{$link}">
				{_btn_category_back}
			</a>
		</p>
	{/if}
</section>


