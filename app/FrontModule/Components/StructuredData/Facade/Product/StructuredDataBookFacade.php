<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData\Facade\Product;

use App\FrontModule\Components\StructuredData\Facade\StructuredDataFacadeInterface;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ProductLocalization\ProductLocalization;

/**
 * @method ProductLocalization getData()
 */
final class StructuredDataBookFacade extends StructuredDataProductDefaultFacade implements StructuredDataFacadeInterface
{

	public function createStructuredData(): array
	{
		return [
			'@context' => $this->createContext(),
			'@type' => $this->createType(),
			'name' => $this->createName(),
			'image' => $this->createImage(),
			'author' => $this->createAuthor(),
			'datePublished' => $this->createPublishDate(),
			'isbn' => $this->createIsbn(),
			'publisher' => $this->createPublisher(),
			'bookFormat' => $this->createBookFormat(),
			'numberOfPages' => $this->createNumberOfPages(),
			'inLanguage' => $this->createLanguage(),
			'offers' => $this->createOffers(),
			'aggregateRating' => $this->createAggregateRating(),
			'review' => $this->createReview(),
		];
	}

	private function createBookFormat(): string|null
	{
		if ($this->getData()->product->isAudiobook) {
			return 'EBook';
		}
		$bindParameter = $this->parameterValueRepository->findValues($this->getData()->product, Parameter::UID_BIND)->fetch();
		if ($bindParameter === null) {
			return null;
		}
		if ($bindParameter->internalAlias === 'pevna') {
			return 'Hardcover';
		}

		return 'Paperback';
	}

	protected function createType(): string
	{
		return 'Book';
	}

}
