{varType App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration $option}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
{var $productDimensions = $variant->product->getDimensions()}

<div n:if="count($options)" class="b-delivery-options__group u-mb-last-0">
	<p class="u-fw-b">
		{_$title}
	</p>
	<ul class="b-delivery-options__list">
		<li n:foreach="$options as $option" class="b-delivery-options__item">
			{var $isAllowed = $option->isAllowed($priceLevel, $state, App\Model\Currency\CurrencyHelper::getCurrency(), $variant->getWeight(), [$productDimensions])}
			{php $imgs = $option->cf->deliveryPayment??->icon ?? []}

			<span class="b-delivery-options__img">
				{capture $sizes}
					{if count($imgs) == 1}
						width="60" height="46"
					{elseif count($imgs) == 2}
						width="60" height="30"
					{elseif count($imgs) == 3}
						width="60" height="22"
					{/if}
				{/capture}

				{foreach $imgs as $img}
					<img src="{$img->getSize('sm')->src}" alt="" loading="lazy" {$sizes|noescape}>
				{/foreach}
			</span>
			<b class="b-delivery-options__title">
				{$option->name}
			</b>
			<span class="b-delivery-options__tooltip">
				{if $option->tooltip}
					{embed $templates.'/part/core/tooltip.latte', class: false, placement: 'right-start'}
						{block btn}
							<button type="button" class="tooltip__info">?</button>
						{/block}
						{block content}
							{$option->tooltip}
						{/block}
					{/embed}
				{/if}
			</span>
			<span class="b-delivery-options__desc">
				{$option->desc}
			</span>

			{php $type = $variant->productAvailability->getType() ?? false}
			{if $type == 'preorder'}
				{php $availabilityClass = 'availability--prepare'}
			{elseif in_array($type, ['onstock', 'onstock_supplier'])}
				{php $availabilityClass = 'availability--available'}
			{else}
				{php $availabilityClass = 'availability--unavailable'}
			{/if}
			<b n:class="b-delivery-options__availability, availability, $availabilityClass">
				{if $option->getDeliveryMethod()->getDeliveryType()->isStore()}
					{var $deliveryText = $variant->productAvailability->getStoreText($state)}
				{else}
					{var $deliveryText = $variant->productAvailability->getDeliveryText($mutation, $state, $priceLevel, App\Model\Currency\CurrencyHelper::getCurrency(), $option)}
				{/if}

				{if $deliveryText !== null}
					{translate($deliveryText)}
				{else}
					unknown
				{/if}

			</b>
			<b class="b-delivery-options__price">
				{if !$isAllowed}
					{_'delivery_option_not_allowed'}
				{else}
					{if $option->isTransitFree($priceLevel, $state, $currency) || ($variant->product->hasFreeTransport() && $option->calculateFree)}
						{_"free_transit"}
					{else}
						{varType Brick\Money\Money $money}
						{var $money = $option->getPrice($priceLevel, $state, $currency)->price->asMoney()}
						{if $money->isZero()}
							{_"free_transit"}
						{else}
							{$money|formatMoney}
						{/if}
					{/if}
				{/if}
			</b>
		</li>
	</ul>
</div>
