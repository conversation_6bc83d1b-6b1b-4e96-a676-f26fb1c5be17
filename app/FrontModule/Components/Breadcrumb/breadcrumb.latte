{default $class = 'u-pt-xxs u-pt-xs@md u-mb-xs u-mb-sm@md'}

<nav n:if="count($breadcrumbs) > 0" n:class="m-breadcrumb, $class">
	{define #bc}
		{default $class = false}
		<p n:class="m-breadcrumb__wrap, u-mb-0, $class">
			<strong class="u-vhide">
				{_breadcrumb_title}
			</strong>

			{foreach $breadcrumbs as $key=>$i}
				{if $i instanceof App\PostType\Page\Model\Orm\Tree}
					{var $nameAnchor = $i->getNameAnchorBreadcrumb()}
				{else}
					{var $nameAnchor = $i->nameAnchor}
				{/if}

				{if !$iterator->first}
					{('angle-right-bold')|icon, 'm-breadcrumb__separator'}
				{/if}
				{if $iterator->last}
					{if $object instanceOf App\Model\ProductVariant}
						<a href="{plink $i, category: null}" class="m-breadcrumb__link">{$i->nameAnchor}</a>
						{('angle-right-bold')|icon, 'm-breadcrumb__separator'}
						<span class="m-breadcrumb__link">{$object->name}</span>
					{else}
						<span class="m-breadcrumb__link">{$nameAnchor}{if isset($_GET[search]) && $iterator->isLast() && $object->uid == 'search'}: {$_GET[search]}{/if}</span>
					{/if}
				{else}
					<a href="{plink $i, category: null, page: null}" class="m-breadcrumb__link">{$nameAnchor}</a>
				{/if}
			{/foreach}
		</p>
	{/define}

	{php $lastCategory = $breadcrumbs[count($breadcrumbs) - 2] ?? false}
	{if $lastCategory}
		{include #bc, class: 'u-d-n u-d-b@md'}
		<p class="m-breadcrumb__wrap u-mb-0 u-d-n@md">
			{('angle-left-bold')|icon, 'm-breadcrumb__separator'}
			<a href="{plink $lastCategory}" class="m-breadcrumb__link">{$lastCategory->nameAnchor}</a>
		</p>
	{else}
		{include #bc}
	{/if}
</nav>
