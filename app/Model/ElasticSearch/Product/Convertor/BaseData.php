<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\PostType\Tag\Model\TagType;
use Nette\Utils\Strings;

class BaseData implements Convertor
{

	public function __construct(
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	public function convert(Product $product): array
	{
		$mutation = $product->getMutation();
		$productLocalization = $product->getLocalization($mutation);

		$data = [
			'productId' => $product->getPersistedId(),
			'url' => $this->linkFactory->linkTranslateToNette($productLocalization, ['mutation' => $mutation]),
			'lastModified' => new \DateTimeImmutable(),
			'nameSort' => $productLocalization->name,
			'name' => $productLocalization->name,
			'nameTitle' => $productLocalization->nameTitle,
			'nameAnchor' => $productLocalization->nameAnchor,
			'content' => ($productLocalization->content) ? strip_tags($productLocalization->content) : '',
			'annotation' => $productLocalization->annotation,
			'isPublic' => (bool) $productLocalization->public,
			'isElectronic' => (bool) $product->isElectronic,
			'isDamaged' => (bool) $product->isDamaged,
			'damageLevel' => (int) $product->damageLevel,
			'soldCount' => $product->soldCount,
			'reviewAverage' => $product->reviewAverage,
			'productType' => $product->productType?->uid,
		];

		$variants = $product->variants->toCollection()->findBy([
			'variantLocalizations->mutation' => $mutation,
		]);

		$data['eans'] = [];
		$data['isbn'] = [];
		$data['codes'] = [];
		$data['variantIds'] = [];

		foreach ($variants as $variant) {
			if ($variant->ean) {
				$data['eans'][] = Strings::lower((string) $variant->ean);
			}
			if ($variant->code) {
				$data['codes'][] = Strings::lower((string) $variant->code);
			}
			if ($variant->isbn) {
				$data['isbn'][] = Strings::lower((string) $variant->isbn);
			}
		}


		return $data;
	}

}
