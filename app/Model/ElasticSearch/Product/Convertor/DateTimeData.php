<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

class DateTimeData implements Convertor
{
	public function __construct(
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{
	}

	public function convert(Product $product): array
	{

		$dateCreated = $datePublished = $product->dateCreated;
		$datePublishedParameterValue = $this->parameterValueRepository->getProductParameterValueByUid($product, 'datum-vydani');
		if($datePublishedParameterValue !== null){
			try {
				$datePublished = new DateTimeImmutable($datePublishedParameterValue->value);
			} catch (Throwable){

			}
		}

		return [
			'publicFrom' => ConvertorHelper::convertTime($product->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($product->publicTo),
			'datePublished' => ConvertorHelper::convertTime($datePublished), // From parameters (Datum vydání)
			'dateCreated' => ConvertorHelper::convertTime($dateCreated),
		];
	}
}
