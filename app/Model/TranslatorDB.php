<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Cache\CacheStorageService;
use App\Model\Cache\StorageService\TranslatorCacheStorageService;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\String\StringRepository;
use App\Model\Orm\Traits\HasStaticCache;
use Nette;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Stringable;
use Throwable;


final class TranslatorDB implements Nette\Localization\Translator
{

	use HasStaticCache;
	public const DONT_INSERT_NEW = 'dont_insert_new';

	private Mutation $mutation;

	protected array $list;

	private array $translateMap;

	private array $usageCache = [];
	private string $pageCacheKey;

	public function __construct(
		private readonly StringRepository $stringRepository,
		private readonly TranslatorDBCacheService $translatorDBCacheService,
		private readonly MutationsHolder $mutationsHolder,
		private readonly TranslatorCacheStorageService $cacheStorageService,
		private readonly ?bool $insertNew = false,
		private readonly ?bool $markUsage = false,
	)
	{
	}

	public function clearTranslationString(string $initialTranslate): string
	{
		return preg_replace('/^##/', '', $initialTranslate);
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function setPageCacheKey(string $pageCacheKey): void
	{
		$this->pageCacheKey = $pageCacheKey;
	}

	public function persistCacheMap(): void
	{
		if (isset($this->pageCacheKey) && isset($this->translateMap)) {
			$generator = function () {
				$options = [
					Nette\Caching\Cache::Tags => [TranslatorDBCacheService::CACHE_TAG_PAGE],
					Nette\Caching\Cache::Expire => '10 hour',
					Nette\Caching\Cache::Sliding => true,
				];
				return [$this->translateMap, $options];
			};
			$this->cacheStorageService->saveDataWithOptions('translatePageCache', $this->pageCacheKey, $generator);
		}
	}

	public function translateFunction(string|\stdClass|TranslateData $data, ?array $params = null, bool $debug = false): string
	{
		$translatedMessage = is_string($data) ? $this->translate($data) : $this->translate($data->message);
		if ($debug) {
			bdump($data, 'latte function translate DATA');
			bdump($translatedMessage, 'latte function translate TRANSLATED MESSAGE');
		}

		$params = is_string($data) ? $params : $data->params;
		$params = is_null($params) ? [] : $params;

		foreach ($params as $key => $param) {
			if ($param instanceof TranslateData) {
				$params[$key] = $this->translate($param->message);
			}
		}

		return str_replace(array_keys($params), array_values($params),$translatedMessage); // @phpstan-ignore-line
	}
	public function translate(string|Stringable $message, mixed ...$parameters): string
	{
		if ($message instanceof TranslateData) {
			return $this->translateFunction($message);
		}

		if ($message instanceof Stringable) {
			$message = (string) $message;
		}

		$translate = $this->tryToGetFromPageCache($message);
		if ($translate == null) {
			$sanitizeKey = self::getKey($message);
			$translate = $this->tryToGetFromStringCacheCache($sanitizeKey);
		}

		$this->translateMap[$message] = $translate;

		if ($translate !== null) {
			if ($this->markUsage) {
				$sanitizeKey = $sanitizeKey ?? self::getKey($message);
				$this->markUsage($sanitizeKey, $this->mutation);
			}
			return $translate;
		}

		//fallback for missing translates
		$this->translatorDBCacheService->save($this->mutation, self::getKey($message), $message);
		if ($this->insertNew && ! (is_array($parameters) && in_array(self::DONT_INSERT_NEW, $parameters)) ) {
			$initialTranslate = '##' . $message;
			$sanitizeKey = $sanitizeKey ?? self::getKey($message);
			foreach ($this->mutationsHolder->findAll() as $mutation) {
				try {
					if (Strings::length($sanitizeKey) > 0) {
						$this->stringRepository->save(null, ['name' => $sanitizeKey, 'value' => $initialTranslate, 'lg' => $mutation->langCode]);
						$this->stringRepository->flush();
					}
				} catch (Throwable) {
					// skip duplicity
				}
			}
		}

		return $message;
	}


	public static function getKey(string $message): string
	{
		$sanitizeKey = Strings::webalize(substr($message, 0, 250), '_', false);
		// return snake_case
		return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $sanitizeKey));
	}



	public function cleanCache(?Mutation $mutation = null): void
	{
		$this->translatorDBCacheService->cleanCache($mutation);
	}

	public function markUsage(string $key, Mutation $mutation): void
	{
		if (isset($this->usageCache[$key][$mutation->id])) {
			return;
		}

		if (($entity = $this->stringRepository->getBy(['name' => $key, 'lg' => $mutation->langCode])) !== null) {
			$this->usageCache[$key][$mutation->id] = 1;
			if (!isset($entity->usedAt) || $entity->usedAt < new DateTimeImmutable('-1day')) {
				$entity->usedAt = new DateTimeImmutable();
				$this->stringRepository->persistAndFlush($entity);
			}
		}
	}

	private function tryToGetFromStringCacheCache(string $sanitizeKey): ?string
	{
		$translate = $this->translatorDBCacheService->load($this->mutation, $sanitizeKey);
		if ($translate !== null) {
			return $translate;
		}

		$row = $this->stringRepository->getRawRow($this->mutation->langCode, $sanitizeKey);
		if ($row === null) {
			return null;
		}
		$clearTranslationString = $this->clearTranslationString($row->value);
		$this->translatorDBCacheService->save($this->mutation, $sanitizeKey, $clearTranslationString);

		return $clearTranslationString;
	}

	private function tryToGetFromPageCache(mixed $message): ?string
	{
		if (!isset($this->pageCacheKey)) {
			return null;
		}
		$map = $this->loadCache(
			'translatePageCache',
			fn() => $this->cacheStorageService->getStoredDataWithOptions('translatePageCache', $this->pageCacheKey, null)
		);
		if (isset($map[$message])) {
			return $map[$message];
		}
		return null;
	}

}
