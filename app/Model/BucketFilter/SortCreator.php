<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\FulltextSearch\SearchMetaInfo;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;

final class SortCreator
{
	public const string TOP = 'top';
	public const string BESTSELLER = 'bestseller';
	public const string CHEAPEST = 'cheapest';
	public const string EXPENSIVE = 'expensive';
	public const string REVIEW = 'review';
	public const string NEWEST = 'newest';
	public const string OLDEST = 'oldest';
	public const string NAME = 'name';
	public const string FULLTEXT = 'fulltextScore';
	public const string SERIES = 'series';
	public const string DISCOUNT = 'discount';
	public const string FULLTEXT_AUTHOR = 'fulltextAuthor';
	public const string FULLTEXT_PUBLISHER = 'fulltextPublisher';
	public const string FULLTEXT_CODE = 'fulltextCode';


	public function create(string $order, State $state, PriceLevel $priceLevel, ?SearchMetaInfo $searchMetaInfo = null): Sort
	{

		bd($order);
		$sort = new Sort($order);
		//$sort->addByStore();
		if ($order === self::TOP) {
			$sort->setIsPrefixed(false);
			$sort->setIsIndexed();
			$sort->addByHasPriceLevelDefault();

			$sort->addByComputedScore();
		//} elseif ($order === 'score') {
        //    $sort->addByScore();
		} elseif ($order === self::BESTSELLER) {
			$sort->setIsPrefixed(false);

			$sort->addByBestseller();
			$sort->addByComputedScore();
		} elseif ($order === self::CHEAPEST) {
			$sort->setIsIndexed();
			$sort->addByHasPriceLevelDefault();
			$sort->addByPrice($state, $priceLevel, 'asc');
		} elseif ($order === self::EXPENSIVE) {
			$sort->setIsIndexed();
			$sort->addByHasPriceLevelDefault();
			$sort->addByPrice($state, $priceLevel, 'desc');
		} elseif ($order === self::REVIEW) {
			$sort->addByReview();
		} elseif ($order === self::NEWEST) {
			$sort->addByHasPriceLevelDefault();
			$sort->setIsIndexed();

			$sort->addByNewest();
		} elseif ($order === self::OLDEST) {
			$sort->addByHasPriceLevelDefault();
			$sort->addByOldest();
		} elseif ($order === self::NAME) {
			$sort->setIsPrefixed(false);
			$sort->setIsSuffixed();
			$sort->addByHasPriceLevelDefault();
			$sort->addByName();
		} elseif (in_array($order, [self::FULLTEXT_CODE, self::FULLTEXT_PUBLISHER, self::FULLTEXT_AUTHOR])) {
			$sort->addByMetaInfo($searchMetaInfo);
			$sort->addByBuyable();
			$sort->addByFulltextScore();
		} elseif ($order === self::FULLTEXT) {
			$sort->addByMetaInfo($searchMetaInfo);
			$sort->addByBuyable();
			$sort->addByFulltextScore();
			$sort->addByComputedScore();
		} elseif ($order === self::SERIES) {
			$sort->addBySeries();
			$sort->setIsIndexed();
		} elseif ($order === self::DISCOUNT) {
			$sort->setIsIndexed(false);
			$sort->setIsPrefixed(false);
			$sort->setIsSuffixed();

			$sort->addByHasPriceLevelDefault();
			$sort->addByDiscount();
			$sort->addByComputedScore();
		}

		return $sort;
	}

	public static function getBuyableSorts(): array
	{
		return [
			self::CHEAPEST,
			self::EXPENSIVE,
			self::OLDEST,
			self::DISCOUNT,
			self::NEWEST,
		];
	}

}
