<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\Box;

use App\Model\BucketFilter\Box\Items\Common;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\RoutableEntity;
use Closure;

class CheckBoxes
{

	public bool $isPlus;

	public string $name;

	private bool $isOpen;

	public bool $showMoreButton;

	public int $showMoreButtonCount = 0;

	public bool $searchValueNotFound = false;

	//private int $maxAllowedValuesCount = 50;

	private array $items;

	private array $checkedItems;


	public array $filterToDeSelect;

	private array $allSelectedValues;

	private array $selectedValues;

	private array $counts;
	private int $optionsCount;

	/**
	 * @phpstan-param Closure(?array $valueIds): array $getOptionFunction
	 */
	public function __construct(
		public readonly string $namespace,
		private readonly Closure $getOptionFunction,
		public readonly string $title = '',
		public readonly string $unit = '',
		public readonly string $description = '',
		public ?int $visibleValuesCount = null,
		private readonly bool $forceOpen = false,
		private readonly bool $isMultiselect = false,
		private readonly bool $onlyNonZeroValues = false,
		public ?RoutableEntity $showMoreLink = null,
		private readonly string $search = '',
		public readonly bool $indexable = false,
	)
	{
	}



	public function populate(string $name, array $selectedValues, array $allSelectedValues, array $counts): void
	{
		$this->allSelectedValues = $allSelectedValues;
		$this->isOpen = ($selectedValues !== []);
		$this->selectedValues = $selectedValues;
		$this->isPlus = ! $this->isMultiselect && ($selectedValues !== []);

		$filterToDeSelect = $allSelectedValues;
		unset($filterToDeSelect[$this->namespace][$name]);
		$this->filterToDeSelect = $filterToDeSelect;

		$this->name = $name;
		$this->counts = $counts;
	}

	public function getName(): string
	{
		return $this->name;
	}


	public function getCheckedItems(): array
	{
		$this->initItems();
		return $this->checkedItems;
	}

	public function getItems(): array
	{
		$this->initItems();
		return $this->items;
	}


	public function initItems(): void
	{
		if (!isset($this->items)) {

			$checkedItems = $possibleItems = [];

			if ($this->selectedValues !== []) {
				foreach ($this->getOptions($this->selectedValues) as $option) {
					$valueId = $option->id;
					$stringValue = $option->name;
					$count = null;
					$checkedItems[]  = new Common($stringValue, $option->filterName ?? $stringValue, $option->filterNameForTitle ?? $stringValue, $option->filterPrefix ?? '', $option->filterSuffix ?? '', $option->unit ?? '', $this->name, $this->allSelectedValues, $valueId, $count, true, $this->namespace);
				}
			}

			foreach ($this->getOptions(array_keys($this->counts)) as $option) {
				$valueId = $option->id;
				$stringValue = $option->name;

				if (isset($this->counts[$valueId])) {
					$count = $this->counts[$valueId];
				} else {
					// skip missing values in elastic aggregation
					continue;
				}

				$isChecked = $this->isValueChecked($valueId);

				if (!$this->onlyNonZeroValues || $count > 0) {

					// skip checked items (solved elsewhere)
					if (!$isChecked) {
						$possibleItems[] = new Common($stringValue, $option->filterName ?? $stringValue, $option->filterNameForTitle ?? $stringValue, $option->filterPrefix ?? '', $option->filterSuffix ?? '', $option->unit ?? '', $this->name, $this->allSelectedValues, $valueId, $count, $isChecked, $this->namespace);
					}
				}
			}

			$this->optionsCount = count($possibleItems) + count($checkedItems); //count($items);

			// if searched query return 0 rows
			if($this->search !== '' && $possibleItems === []){
				$this->searchValueNotFound = true;
			}

			$items = array_merge($checkedItems, $possibleItems);

			$this->showMoreButton = $this->visibleValuesCount !== null && $this->visibleValuesCount < $this->optionsCount;
			$this->showMoreButtonCount = $this->optionsCount - $this->visibleValuesCount;
			$this->items = $items;
			$this->checkedItems = $checkedItems;
		}
	}

	private function getOptions(array $ids): array
	{
		$getOptionFunction = $this->getOptionFunction;
		return $getOptionFunction($ids);
	}

	public function getOptionsCount(): int
	{
		return $this->optionsCount;
	}


	public function isOpen(): bool
	{
		return $this->forceOpen || $this->isOpen || $this->hasSearchActive();
	}

	public function hasSearch(): bool
	{
		return count($this->counts) >= DiscreteValues::MAX_TERMS_LIMIT || $this->search !== '';
	}

	public function hasSearchActive() : bool
	{
		return $this->search !== '';
	}

	public function searchValue(): string
	{
		return $this->search;
	}


	public function isValueChecked(int $valueId): bool
	{
		return (in_array($valueId, $this->selectedValues));
	}

}
