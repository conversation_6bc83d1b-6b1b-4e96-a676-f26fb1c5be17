<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Gift;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Brick\Math\BigDecimal;
use Brick\Money\Money;

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$gifts}
 * @property GiftLocalization|null $giftLocalization {m:1 GiftLocalization, oneSided=true}
 * @property string|null $giftName {default null}
 */
final class GiftItem extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, GiftLocalization $giftLocalization): GiftItem
	{
		$item = new self();
		$item->order = $order;
		$item->giftLocalization = $giftLocalization;
		$item->amount = 1;
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();
		$item->giftName = $giftLocalization->getName();

		return $item;
	}

	public static function createFromErp(Order $order, ?GiftLocalization $giftLocalization, string $giftName, Price $unitPrice, VatRate $vatRate, BigDecimal $vatRateValue, int $amount = 1): GiftItem
	{
		$item = new self();
		$item->order = $order;
		$item->giftLocalization = $giftLocalization;
		$item->giftName = $giftName;
		$item->amount = $amount;
		$item->unitPrice = $unitPrice;
		$item->vatRate = $vatRate;
		$item->vatRateValue = $vatRateValue;

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return $this->giftLocalization->isActive($this->order) && $this->giftLocalization->isAvailable($this->order) /*&& !$this->order->hasGift()*/ ? 1 : 0;
	}

	public function getCurrentUnitPrice(): Money
	{
		return $this->giftLocalization->gift->price->asMoney();
	}

	protected function getCurrentVatRate(): VatRate
	{
		if (($product = $this->giftLocalization->gift->product) !== null) {
			return $product->vatRateType($this->order->country);
		}
		return VatRate::None;
	}

	public function getName(): string
	{
		if ($this->giftName !== null) {
			return $this->giftName;
		}
		return $this->giftLocalization->getName();
	}

	public function setAmount(int $amount): void
	{
		$amount = min($amount, $this->getMaxAvailableAmount());
		$this->amount = $amount;
	}

}
