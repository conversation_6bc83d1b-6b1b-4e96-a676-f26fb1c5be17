<?php

declare(strict_types=1);

namespace App\Model\Orm\Parameter;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Parameter>
 */
final class ParameterMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'parameter';
}

	// add mapping
	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->manyHasManyStorageNamePattern = '%s_%s';

		return $conventions;
	}

	/**
	 * @return ICollection<Parameter>
	 */
	public function searchByName(string $search, array $selectedSiblingsIds): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $search);

		if ($selectedSiblingsIds) {
			$builder->andWhere('id not in %i[]', $selectedSiblingsIds);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Parameter>
	 */
	public function findByUidOrdered(array $uids): ICollection
	{
		$tmpUids = array_map(function ($uid) {
			return "'" . $uid . "'";
		}, $uids);
		$builder = $this->builder()
			->andWhere('uid in %s[]', $uids)
			->orderBy('%raw', 'FIELD(uid, ' . implode(',', $tmpUids) . ')');
		return $this->toCollection($builder);
	}


	public function findProductParameterValueIds(Parameter $parameter): Result
	{
		$builder = $this->builder();
		$builder->select('parameterValueId')->from('product_parameter')
			->andWhere('parameterId = %i', $parameter->id);

		return $this->connection->queryByQueryBuilder($builder);
	}

	/**
	 * @return ICollection<Parameter>
	 */
	public function findParametersForProduct(Product $product): ICollection
	{
		$builder = $this->builder();
		$builder->select('p.*')
			->from('product_parameter as pp')
			->joinInner('[parameter] as p', '[p.id] = [pp.parameterId]')
			->andWhere('productId = %i', $product->id)
			->orderBy('p.sort')
			->groupBy('parameterId');

		return $this->toCollection($builder);
	}
	/**
	 * @return ICollection<Parameter>
	 */
	public function findDetailParametersForProduct(Product $product): ICollection
	{
		$builder = $this->builder();
		$builder->select('p.*')
		        ->from('product_parameter as pp')
		        ->joinInner('[parameter] as p', '[p.id] = [pp.parameterId]')
		        ->andWhere('productId = %i', $product->id)
				->andWhere('p.isInDetail = %i', 1)
		        ->orderBy('p.sort')
		        ->groupBy('parameterId');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Parameter>
	 */
	public function findListViewParametersForProduct(Product $product): ICollection
	{
		$builder = $this->builder();
		$builder->select('p.*')
		        ->from('product_parameter as pp')
		        ->joinInner('[parameter] as p', '[p.id] = [pp.parameterId]')
		        ->andWhere('productId = %i', $product->id)
		        ->andWhere('p.isInListView = %i', 1)
		        ->orderBy('p.sort')
		        ->groupBy('parameterId');

		return $this->toCollection($builder);
	}


	/**
	 * @return ICollection<Parameter>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

	public function initSort(bool $forceInit = false): void
	{
		$builder = $this->builder();
		$builder->where('sort = %i', 0)->limitBy(1);

		$row = $this->connection->queryByQueryBuilder($builder)->fetch();

		if ($row !== null || $forceInit) {
			$b = $this->builder()->select('id')->orderBy('sort ASC');
			$i = 1;
			foreach ($this->connection->queryByQueryBuilder($b) as $parameter) {
				$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), $i, $parameter->id);
				$i++;
			}
		}
	}

	public function moveUp(int $siblingSort, int $sort): void
	{
		$b = $this->builder()->select('id, sort')->where('sort <= %i AND sort > %i', $siblingSort, $sort);
		foreach ($this->connection->queryByQueryBuilder($b) as $row) {
			$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), ($row->sort - 1), $row->id);
		}
	}

	public function moveDown(int $siblingSort, int $sort): void
	{
		$b = $this->builder()->select('id, sort')->where('sort >= %i AND sort < %i', $siblingSort, $sort);
		foreach ($this->connection->queryByQueryBuilder($b) as $row) {
			$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), ($row->sort + 1), $row->id);
		}
	}

	public function delete(Parameter $parameter): void
	{
		$this->connection->transactional(function (Connection $connection) use ($parameter) {
			$connection->query('DELETE FROM product_parameter WHERE parameterId = %i', $parameter->id);
			$connection->query('DELETE FROM parameter_value WHERE parameterId = %i', $parameter->id);
			$connection->query('DELETE FROM parameter WHERE id = %i', $parameter->id);
		});

	}

	public function unlockParameters(): void
	{
		$this->connection->query('UPDATE parameter SET isLockedForES = 0');
	}

}
