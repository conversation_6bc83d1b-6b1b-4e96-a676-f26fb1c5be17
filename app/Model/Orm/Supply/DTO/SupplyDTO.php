<?php

namespace App\Model\Orm\Supply\DTO;

use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;

class SupplyDTO
{

	public function __construct(
		public readonly int $id,
		public readonly int $stockId,
		public readonly int $variantId,
		public readonly int $amount,
		public readonly int $deliveryDelay,
		public readonly ?int $supplierId,
		public readonly DateTimeImmutable $lastImport,
		public readonly ?DateTimeImmutable $lastOnStock,
		public readonly ?DateTimeImmutable $stockDate,
	)
	{
	}

	public static function createFromRow(Row $supply): self
	{
		return new self(
			$supply->id,
			$supply->stockId,
			$supply->variantId,
			$supply->amount,
			$supply->deliveryDelay,
			$supply->supplierId,
			$supply->lastImport,
			$supply->lastOnStock,
			$supply->stockDate,
		);
	}
}
