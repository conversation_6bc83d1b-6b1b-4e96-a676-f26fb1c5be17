<?php declare(strict_types = 1);

namespace App\Model\Orm\Mutation;

use App\Model\Orm\Functions\LikeFilterFunction;
use Nextras\Orm\Collection\Functions\IArrayFunction;
use Nextras\Orm\Collection\Functions\IQueryBuilderFunction;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Mutation|null getById($id)
 *
 * @extends Repository<Mutation>
 */
final class MutationRepository extends Repository
{

	static function getEntityClassNames(): array
	{
		return [Mutation::class];
	}


	public function getByCode(string $code): Mutation
	{
		return $this->getByChecked(['langCode' => $code]);
	}

	public function getDefault(): Mutation
	{
		return $this->getByCode(Mutation::DEFAULT_CODE);
	}

	public function getRsDefault(): Mutation
	{
		return $this->getByCode(Mutation::DEFAULT_RS_CODE);
	}
	/**
	 * @return ICollection<Mutation>
	 */
	public function findByTreeNodeSort(): ICollection
	{
		return $this->findBy([
			'trees->parent' => null,
		])->orderBy('trees->sort');

	}

	/**
	 * @return ICollection<Mutation>
	 */
	public function findAllWithRsOrder(): ICollection
	{
		return $this->findAll();
	}

}
