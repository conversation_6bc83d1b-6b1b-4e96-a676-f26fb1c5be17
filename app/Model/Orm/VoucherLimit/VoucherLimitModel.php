<?php
declare(strict_types=1);

namespace App\Model\Orm\VoucherLimit;


use A\B;
use App\AdminModule\Presenters\ProductBulk\Components\Actions\ProductIdsFinder;
use App\Exceptions\LogicException;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Translator;
use Closure;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;
use Generator;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

final class VoucherLimitModel {

	use HasCache;

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ProductIdsFinder $productIdsFinder,
	)
	{
	}

	/**
	 * @return Closure(): Generator
	 */
	public function executeElasticQuery(BoolQuery $boolQuery): Closure
	{
		return $this->productIdsFinder->getIteratorProductIds($this->mutationsHolder->getDefault(), $boolQuery);
	}

	/**
	 * @param ICollection<VoucherLimit> $limits
	 */
	private function getNegatedLimits(ICollection $limits): array
	{
		return $this->loadCache($this->createCacheKey('negated', $limits), function () use ($limits) {
			$negated = []; $alpha = 'A';
			/** @var VoucherLimit $limit */
			foreach ($limits as $limit) {
				if ($limit->isNegated) {
					$negated[$alpha] = $limit;
				}
				$alpha++;
			}

			return $negated;
		});
	}

	/**
	 * @param ICollection<VoucherLimit> $limits
	 */
	private function getDefaultLimits(ICollection $limits): array
	{
		return $this->loadCache($this->createCacheKey('default', $limits), function () use ($limits) {
			$default = []; $alpha = 'A';
			/** @var VoucherLimit $limit */
			foreach ($limits as $limit) {
				if (!$limit->isNegated) {
					$default[$alpha] = $limit;
				}
				$alpha++;
			}

			return $default;
		});
	}

	/**
	 * @param ICollection<VoucherLimit> $limits
	 */
	private function getConditions(ICollection $limits): array
	{
		return $this->loadCache($this->createCacheKey('conditions', $limits), function () use ($limits) {
			$negated = $this->getNegatedLimits($limits);
			$default = $this->getDefaultLimits($limits);

			$condition = [];
			foreach ($default as $key => $value) {
				$condition[$key][$key] = $this->getLimitCondition($value);
				foreach ($negated as $keyNegated => $valueNegated) {
					$condition[$key][$keyNegated] = $this->getLimitCondition($valueNegated);
				}
			}

			if ($condition === [] && $negated !== []) {
				foreach ($negated as $keyNegated => $valueNegated) {
					$condition[$keyNegated][$keyNegated] = $this->getLimitCondition($valueNegated);
				}
			}

			return $condition;
		});
	}
	/**
	 * @param ICollection<VoucherLimit> $limits
	 */
	public function getElasticCondition(ICollection $limits): BoolQuery
	{
		$condition = $this->getConditions($limits);

		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();

		foreach ($condition as $values) {
			$boolAndQuery = $b->query()->bool();
			foreach ($values as $value) {
				$boolAndQuery->addMust($value);
			}
			$boolQuery->addShould($boolAndQuery)->setMinimumShouldMatch(1);
		}

		return $boolQuery;
	}


	private function getLimitCondition(VoucherLimit $limit): BoolQuery
	{
		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();

		$filterToTerm = [
			'categories' => 'path',
			'products' => 'id',
			'tags' => 'tags',
			'productTypes' => Parameter::UID_PRODUCT_TYPE,
		];

		foreach ($filterToTerm as $filter => $term) {
			if(isset($limit->limit->{$filter}) && ($items = $limit->limit->{$filter}) !== []) {
				if(!$limit->isNegated) {
					$boolQuery->addMust($b->query()->terms($term)->setTerms($items));
				} else {
					$boolQuery->addMustNot($b->query()->terms($term)->setTerms($items));
				}
			}
		}

		$filterNestedToTerm = [
			'writers' => Parameter::UID_WRITER,
			'themes' => Parameter::UID_THEME,
		];

		$terms = [
			'writer' => 'writersWithInfo',
			'theme' => 'themesWithInfo',
		];

		foreach ($filterNestedToTerm as $filter => $term) {
			if(isset($limit->limit->{$filter}) && ($items = $limit->limit->{$filter}) !== []) {
				$term = $terms[$term]; // ?? $term;
				if(!$limit->isNegated) {
					$boolQuery->addMust($b->query()->nested()->setPath($term)->setQuery(
						$b->query()->terms($term.'.id', $items)
					));
				} else {
					$boolQuery->addMustNot($b->query()->nested()->setPath($term)->setQuery(
						$b->query()->terms($term.'.id', $items)
					));
				}
			}
		}

		return $boolQuery;
	}
	/**
	 * @param ICollection<VoucherLimit> $limits
	 */
	public function getTextCondition(ICollection $limits): string
	{
		return $this->loadCache($this->createCacheKey('textCondition', $limits), function () use ($limits) {
			$negated = $this->getNegatedLimits($limits);
			$condition = $this->getConditions($limits);

			$orCodnditions = [];
			foreach ($condition as $boolQueries) {
				$orCodnditions[] = '(' . implode(' and ', array_keys($boolQueries)). ')';
			}

			if($orCodnditions === [] && $negated !== []) {
				$negatedConditions = array_keys($negated);
				foreach ($negatedConditions as $k => $negatedCondition) {
					$negatedConditions[$k] = '!' . $negatedCondition;
				}

				return '(' . implode(' and ', $negatedConditions). ')';
			}

			if($orCodnditions === []) {
				$orCodnditions[] = $this->translator->translate('voucher_no_coditions');
			}


			return implode(' or ', $orCodnditions);
		});
	}
}
