<?php declare(strict_types = 1);

namespace App\Model\Orm\String;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasTemplateCache;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $lg {default 'cs'}
 * @property int $hidden {default 0}
 * @property string $name
 * @property string $value
 * @property DateTimeImmutable|null $usedAt
 *
 * RELATIONS
 *
 * VIRTUAL
 */
class StringEntity extends Entity
{
	use HasTemplateCache;

	public function getTemplateCacheTagsCascade(): array
	{
		return [];
	}
}
