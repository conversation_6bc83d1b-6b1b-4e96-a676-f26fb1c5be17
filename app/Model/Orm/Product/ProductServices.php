<?php

namespace App\Model\Orm\Product;

use App\Model\Cache\ProductCache;
use App\Model\Comparators\ComparatorModeProvider;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\LibraryImage\FlagImageService;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\ProductVariant\Availability\ProductAvailabilityService;
use App\Model\Orm\Rate\RateModel;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\Supply\SupplyRepository;
use App\Model\Orm\User\UserProvider;
use App\Model\Price\ProductPriceModel;
use App\Model\Product\ProductText;
use App\Model\Template\PartCondition;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalizationRepository;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Publisher\Model\Orm\PublisherLocalization\PublisherLocalizationRepository;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Writer\Model\Orm\WriterLocalization\WriterLocalizationRepository;

readonly class ProductServices
{
	public function __construct(
		public TreeRepository $treeRepository,
		public ParameterRepository $parameterRepository,
		public SupplyRepository $supplyRepository,
		public ProductRepository $productRepository,
		public MutationHolder $mutationHolder,
		public CatalogTreeModel $catalogTreeModel,
		public StateModel $stateModel,
		public PriceLevelModel $priceLevelModel,
		public WriterLocalizationRepository $writerLocalizationRepository,
		public ProductAvailabilityService $productAvailabilityService,
		public TagRepository $tagRepository,
		public GiftLocalizationRepository $giftLocalizationRepository,
		public ParameterValueRepository $parameterValueRepository,
		public FlagImageService $flagImageService,
		public PartCondition $partCondition,
		public ProductPriceModel $productPriceModel,
		public ProductCache $productCache,
		public PublisherLocalizationRepository $publisherLocalizationRepository,
		public ProductText $productText,
		public ComparatorModeProvider $comparatorModeProvider,
		public UserProvider $userProvider,
		public RateModel $rateModel,
	)
	{

	}
}
