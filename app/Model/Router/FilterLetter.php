<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\PostType\Page\Model\Orm\Tree;
use Nette\Utils\Strings;

class FilterLetter
{

	public const VIEWS_UUID_WITH_LETTER_FILTER = [Tree::UID_THEME, Tree::UID_WRITERS, Tree::UID_PUBLISHER, Tree::UID_SERIES];

	public function __construct(
		private readonly FilterCommonParameters $filterCommonParameters,
		private readonly FilterLang $filterLang,
		private readonly FilterAlias $filterAlias,
	)
	{
	}


	public function in(array $params): array
	{
		$params = $this->filterCommonParameters->in($params);
		$params = $this->filterLang->in($params);

		if (!isset($params['mutation'])) {
			$params['presenter'] = false;
			return $params;
		}

		$params = $this->filterAlias->in($params);
		if ($params['presenter'] === false) {
			return $params;
		}

		$letter = $this->resolveLetter($params);

		if ($letter === null) {
			$params['presenter'] = false;
			return $params;
		}
		$params['letter'] = $letter;
		return $params;
	}

	public function out(array $params): ?array
	{
		if (!isset($params['letter'])) {
			return null;
		}

		$params = $this->filterAlias->out($params);
		$params = $this->filterCommonParameters->out($params);

		return $params;
	}


	private function resolveLetter(array $params): ?string
	{
		$letter = urldecode($params['letter']); //Url::unescape($params['letter']);

		if (Strings::length($letter) > 1) {
			return null;
		}

		return $letter;
	}

}
