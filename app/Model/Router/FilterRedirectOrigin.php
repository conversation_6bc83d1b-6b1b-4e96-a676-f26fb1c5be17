<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\Model\Orm\Alias\Alias;
use App\Model\Orm\Alias\AliasRepository;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductRedirect\ProductRedirect;
use App\Model\Orm\ProductRedirect\ProductRedirectRepository;
use App\Model\Orm\RoutableEntity;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Publisher\Model\Orm\PublisherLocalization\PublisherLocalizationRepository;
use App\PostType\Series\Model\Orm\SeriesLocalization\SeriesLocalizationRepository;
use App\PostType\Writer\Model\Orm\WriterLocalization\WriterLocalizationRepository;
use Nette\Utils\Strings;
use Nette\Utils\Validators;

final class FilterRedirectOrigin
{

	public const REGEX_PRODUCT = '/.+\-([0-9]+)\.html/';

	public const REGEX_WRITER = '/\/*autori\/([a-z\-]+)\-([0-9]+)\.htm/';

	public const REGEX_PUBLISHER = '/nakladatel\/([a-z\-]+)\-([0-9]+)/';

	public const REGEX_CATEGORY = '/.+\-([0-9]+)/';

	public const REGEX_SERIES = '/serie\/([a-z\-]+)\.htm/';

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly ProductRedirectRepository $productRedirectRepository,
		private readonly WriterLocalizationRepository $writerLocalizationRepository,
		private readonly PublisherLocalizationRepository $publisherLocalizationRepository,
		private readonly SeriesLocalizationRepository $seriesLocalizationRepository,
		private readonly TreeRepository $treeRepository,
		private readonly FilterLang $filterLang,
		private readonly AliasRepository $aliasRepository,
	)
	{
	}

	final public function in(array $params): array|null
	{
		$params = $this->filterLang->in($params);

		if (!isset($params['mutation'])) {
			// terminate request
			$params['presenter'] = false;
			return $params;
		}

		if (!isset($params['alias'])) {
			return null;
		}

		if ($this->isMatchTheme($params)) {
			return null;
		}

		if ($this->isMatchTag($params)) {
			return null;
		}

		if ($publisherRedirect = $this->resolvePublisher($params)) {
			return $publisherRedirect;
		}

		if ($writerRedirect = $this->resolveWriter($params)) {
			return $writerRedirect;
		}

		if ($productDetailRedirect = $this->resolveProductDetail($params)) {
			return $productDetailRedirect;
		}
		if ($productDetailRedirect = $this->getByAliasGuess($params)) {
			return $productDetailRedirect;
		}

		if ($seriesRedirect = $this->resolveSeries($params)) {
			return $seriesRedirect;
		}

		if ($categoryRedirect = $this->resolveCategory($params)) {
			return $categoryRedirect;
		}

		// TODO: check product redirects (code_redirect)
		if (($productCodeRedirect = $this->resolveProductCodeRedirect($params))) {
			return $productCodeRedirect;
		}
		dumpe($params);

		return null;
	}
	private function resolveProductCodeRedirect(array $params): array
	{
		$alias = $this->aliasRepository->getBy(['alias' => $params['alias'], 'mutation' => $params['mutation'], 'module' => Alias::MODULE_PRODUCT]);
		if ($alias !== null) {
			$repository = $this->productRepository;
			$repository->setPublicOnly(false);

			$result = $repository->getById($alias->referenceId)->getLocalization($params['mutation']);
			$params['object'] = $result;
			$tmp = explode(':', ltrim($result->template, ':'));
			if (count($tmp) === 4) {
				$params['module'] = null;
				$params['presenter'] = $tmp[0] . ':' . $tmp[1] . ':' . $tmp[2];
				$params['action'] = $tmp[3];
			} else {
				$params['module'] = null;
				$params['presenter'] = $tmp[0] . ':' . $tmp[1];
				$params['action'] = $tmp[2];
			}
			return $params;
		}
		dump($alias->alias);
		return [];
	}
	private function resolveProductDetail(array $params): array
	{
		$productExtId = $this->findExtId($params['alias'], self::REGEX_PRODUCT);

		if ($productExtId === null) {
			return [];
		}

		/** @var ProductRedirect|null $redirect */
		$redirect = $this->productRedirectRepository->getBy(['erpId' => $productExtId]);
		//dumpe($redirect);

		if ($redirect !== null) {
			$product = $redirect->product;
		} else {
			$product = $this->productRepository->getByExtId($productExtId) ?? $this->productRepository->getByLegacyId($productExtId);
			if ($product === null) {
				return [];
			}
		}

		$productLocalization = $product->getLocalization($params['mutation']);

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Front:Product',
			'object' => $productLocalization,
			'alias' => $productLocalization->alias?->alias,
		];
	}

	final public function resolveWriter(array $params): array
	{
		$writerExtId = $this->findExtId($params['alias'], self::REGEX_WRITER, 2);

		if ($writerExtId === null) {
			return [];
		}

		if (!$writerLocalization = $this->writerLocalizationRepository->getBy(['erpId' => $writerExtId])) {
			return [];
		}

		if (!isset($writerLocalization->alias->alias)) {
			return [];
		}

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Writer:Front:Writer',
			'action' => 'detail',
			'object' => $writerLocalization,
			'alias' => $writerLocalization->alias->alias,
		];
	}

	final public function isMatchTheme(array $params): bool
	{
		return str_starts_with($params['alias'], 'tema/');
	}

	final public function isMatchTag(array $params): bool
	{
		return str_starts_with($params['alias'], 'stitky/');
	}

	final public function resolvePublisher(array $params): array
	{
		$publisherExtId = $this->findExtId($params['alias'], self::REGEX_PUBLISHER, 2);

		if ($publisherExtId === null) {
			return [];
		}

		if (!$publisherLocalization = $this->publisherLocalizationRepository->getBy(['extId' => $publisherExtId])) {
			return [];
		}

		if (!isset($publisherLocalization->alias->alias)) {
			return [];
		}

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Publisher:Front:Publisher',
			'action' => 'detail',
			'object' => $publisherLocalization,
			'alias' => $publisherLocalization->alias->alias,
		];
	}

	final public function resolveCategory(array $params): array
	{
		$cateogryExtId = $this->findExtId($params['alias'], self::REGEX_CATEGORY);

		if ($cateogryExtId === null) {
			return [];
		}

		if (!$categoryLocalization = $this->treeRepository->getBy(['extId' => $cateogryExtId])) {
			return [];
		}

		if (!isset($categoryLocalization->alias->alias)) {
			return [];
		}

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Front:Catalog',
			'action' => 'detail',
			'object' => $categoryLocalization,
			'alias' => $categoryLocalization->alias->alias,
		];
	}

	public function resolveSeries(array $params): array
	{
		$seriesExtId = Strings::match($params['alias'], self::REGEX_SERIES)[1] ?? null;
		if ($seriesExtId === null) {
			return [];
		}

		if (($seriesLocalization = $this->seriesLocalizationRepository->getBy(['series->uid' => $seriesExtId])) === null) {
			return [];
		}

		if (!isset($seriesLocalization->alias->alias)) {
			return [];
		}

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Series:Front:Series',
			'action' => 'detail',
			'object' => $seriesLocalization,
			'alias' => $seriesLocalization->alias->alias,
		];
	}

	final public function findExtId(string $alias, string $regex, int $resultPosition = 1): int|null
	{
		$idParts = Strings::match($alias, $regex);

		if (!$extId = $idParts[$resultPosition] ?? null) {
			return null;
		}

		if (!Validators::isNumeric($extId)) {
			return null;
		}

		return (int) $extId;
	}

	private function getByAliasGuess(array $params): array
	{
		if (isset($params['alias']) && $idParts = Strings::match($params['alias'], self::REGEX_PRODUCT)) {
			$alias = $params['alias'];
			$alias = preg_replace('/-' . $idParts[1] . '\.html$/', '', $alias);

			$productLocalization = $this->aliasRepository->getBy([
				'alias' => $alias,
				'mutation' => $params['mutation'],
				'module' => Alias::MODULE_PRODUCT,
			])?->parent;

			if ($productLocalization === null) {
				return [];
			}

			return [
				'mutation' => $params['mutation'],
				'presenter' => 'Front:Product',
				'object' => $productLocalization,
				'alias' => $productLocalization->alias->alias,
			];
		}

		return [];
	}



}
