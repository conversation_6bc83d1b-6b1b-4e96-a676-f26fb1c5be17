<?php

declare(strict_types=1);

namespace App\Model\Router;

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Alias\Alias;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\AliasHistory\AliasHistory;
use App\Model\Orm\Orm;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\TranslatorDB;

final class FilterAlias
{

	use HasStaticCache;

	public function __construct(
		private readonly string $adminAlias,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly AliasModel $aliasModel,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	/**
	 * Sanitize alias from filter params: "alias/f_fname.fvalue_f" transform to only "alias"
	 *
	 * @param string $alias
	 * @return string
	 */
	private function sanitizeAlias(string $alias): string
	{
		$aliasParts = explode('/', $alias);
		if (!isset($aliasParts[1])) {
			return $alias;
		}

		if ( (!str_starts_with($aliasParts[1], 'f_') && !str_ends_with($aliasParts[1], '_f')) && (!str_starts_with($aliasParts[1], 'tag-')/* && !str_ends_with($aliasParts[1], ',')*/)) {
			return $alias;
		}

		return $aliasParts[0];
	}

	public function in(array $params, bool $history = false): array
	{
		$params['presenter'] = false;
		$params['action'] = 'default';

		if (isset($params['alias']) && str_starts_with($params['alias'], $this->adminAlias)) {
			return $params;
		}

		if ($history) {
			$repository = $this->orm->aliasHistory;
		} else {
			$repository = $this->orm->alias;
		}

		$alias = $repository->getBy([
			'alias' => $this->sanitizeAlias($params['alias']),
			'mutation' => $params['mutation'],
		]);

		$object = null;
		if (isset($alias)) {

			$object = $this->getObject($alias, $params);
			dumpe($alias->alias, $object);
			if ($object !== null) {
				$this->translator->setPageCacheKey($object::class . '/' . $object->getId());
				$params['object'] = $object;

				$tmp = explode(':', ltrim($object->template, ':'));
				if (count($tmp) === 4) {
					$params['module'] = null;
					$params['presenter'] = $tmp[0] . ':' . $tmp[1] . ':' . $tmp[2];
					$params['action'] = $tmp[3];
				} else {
					$params['module'] = null;
					$params['presenter'] = $tmp[0] . ':' . $tmp[1];
					$params['action'] = $tmp[2];
				}
			}
		} else {
			if (isset($params['alias']) && $params['alias'] === '') {
				trigger_error('Alias missing in alias table for this HP', E_USER_NOTICE);
			}
		}

		if ($object) {
			if ($history) {
				// this will enforce CANONICAL url
				$params['alias'] = $object->alias->alias;
			}
		}

		return $params;
	}

	public function out(array $params, bool $history = false): array
	{
		if (
			isset($params['object']) && ! isset($params['alias'])
		) {
			if (isset($params['mutation'])) {
				$mutation = $params['mutation'];
			} else {
				$mutation = $this->mutationHolder->getMutation();
			}
			$params['alias'] = $this->aliasModel->getStringAlias($mutation, $params['object']->module, $params['object']->id) ?? $params['object']->alias?->alias;
		}

		return $params;
	}


	private function getObject(Alias|AliasHistory $alias, array $params = []): ?RoutableEntity
	{
		return $this->loadCache($this->createCacheKey('aliasObject', $alias, $params), function () use ($alias, $params): ?RoutableEntity {
			$repository = $this->orm->getRepositoryByName($alias->module);
			if (isset($params['show']) && method_exists($repository, 'setPublicOnly')) {
				$repository->setPublicOnly(false);
			}

			$result = $repository->getById($alias->referenceId);
			\assert($result instanceof RoutableEntity || $result === null);

			return $result;
		});
	}

}
