<?php declare(strict_types=1);

namespace App\Model\FulltextSearch;

class SearchMetaInfo
{

	public function __construct(
		public readonly ?array $productNameTotalMatch = [],
		public readonly ?array $authorNameTotalMatch = [],
		public readonly ?array $publisherNameTotalMatch = [],
		public readonly ?array $eanNameTotalMatch = [],
		public readonly ?array $isbnNameTotalMatch = [],
		public readonly ?array $categoryNameTotalMatch = [],
		public readonly ?array $themeNameTotalMatch = [],
		public readonly ?array $tagNameTotalMatch = [],
		public readonly ?array $seriesNameTotalMatch = [],
	)
	{
	}


	public function getIdsByPriority(): array
	{
		bd($this);
		$itemPriorities = [
			$this->eanNameTotalMatch,
			$this->isbnNameTotalMatch,
			$this->authorNameTotalMatch,
			$this->categoryNameTotalMatch,
			$this->tagNameTotalMatch,
			$this->themeNameTotalMatch,
			$this->publisherNameTotalMatch,
			$this->productNameTotalMatch,
			$this->seriesNameTotalMatch,
		];

		foreach ($itemPriorities as $item) {
			if ($item !== []) {
				return $item;
			}
		}

		return [];
	}

}
