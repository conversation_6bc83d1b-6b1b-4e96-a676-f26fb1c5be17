<?php

namespace App\Model\FulltextSearch;

use App\Model\ElasticSearch\Product\Convertor\FulltextData;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;

class QueryProvider
{

	public function getBySearchMetaInfo(SearchMetaInfo $searchMetaInfo, string $textToSearch): AbstractQuery
	{
		$fulltext = FulltextData::normalizeString($textToSearch);

		$priorityIds = $searchMetaInfo->getIdsByPriority();
		if ($priorityIds === []) {
			return $this->getCommonQuery($fulltext);

		}

		return (new QueryBuilder())->query()->bool()
			->addShould($this->getByIdQuery($priorityIds))
			->addShould($this->getCommonQuery($fulltext))
			->setMinimumShouldMatch(1);
	}


	public function getCommonQuery(string $fulltext): BoolQuery
	{
		$b = new QueryBuilder();
		$fields = [];
		$fields['fulltext-name.baseEdgeNgram'] = 3;
		$fields['fulltext-name.baseEdgeNgramAscii'] = 1;
		$fields['fulltext-publisher.baseEdgeNgram'] = 1;

		$baseQuery = $b->query()->bool();

		foreach ($fields as $field => $priority) {
			$subQuery = $b->query()->bool();
			foreach (explode(' ', $fulltext) as $part) {
//				$subQuery->addMust($b->query()->match($field, $part));
				$subQuery->addMust($b->query()->term()->setTerm($field, $part));
			}
			$baseQuery->addShould($subQuery)->setBoost($priority);
		}
		$nestedFields = [];
		$nestedFields['fulltext-writers'] = [
			'priority' => 0.5,
			'field' => 'name.baseEdgeNgram',
		];

		foreach ($nestedFields as $path => $params) {
			foreach (explode(' ', $fulltext) as $part) {
				$baseQuery->addShould(
					$b->query()->nested()->setPath($path)->setQuery(
						$b->query()->term()->setTerm($path . '.' . $params['field'], $part)
					)
				)->setBoost($params['priority']);
			}
		}

		return $b->query()->bool()
			->addMust($baseQuery)
			->addMust(
				$b->query()->bool()->addShould(
					$b->query()->range('score', ['lt' => 20, 'boost' => 0])
				)->addShould(
					$b->query()->range('score', ['gte' => 20, 'boost' => 1.2])
				)->setMinimumShouldMatch(1)
			);
	}

	private function getByIdQuery(array $ids): BoolQuery
	{
		$b = new QueryBuilder();
		return $b->query()->bool()->addMust(
			$b->query()->terms('_id', $ids)
		);
	}

}
