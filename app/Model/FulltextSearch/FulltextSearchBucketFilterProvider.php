<?php

namespace App\Model\FulltextSearch;

use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Product\Convertor\FulltextData;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Elastica\QueryBuilder;
use Elastica\ResultSet;
use Nette\Application\UI\Presenter;

class FulltextSearchBucketFilterProvider
{

	public const string WRITER_TOTAL_MATCH = 'writerTotalMatch';
	public const string PRODUCT_TOTAL_MATCH = 'productTotalMatch';
	public const string PUBLISHER_TOTAL_MATCH = 'publisherTotalMatch';
	public const string ISBN_TOTAL_MATCH = 'isbnTotalMatch';
	public const string EAN_TOTAL_MATCH = 'eanTotalMatch';
	public const string CATEGORY_TOTAL_MATCH = 'categoryTotalMatch';
	public const string THEME_TOTAL_MATCH = 'themeTotalMatch';
	public const string TAG_TOTAL_MATCH = 'tagTotalMatch';
	public const string SERIES_TOTAL_MATCH = 'seriesTotalMatch';

	public function __construct(
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly Repository $esProductRepository,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly SortProvider $sortProvider,
	)
	{
	}

	public function get(string $stringToSearch, Presenter $presenter, Mutation $mutation, State $state, PriceLevel $priceLevel, array $parameters, string $order = SortCreator::FULLTEXT): BucketFilterBuilder
	{
		$searchMetaInfo = $this->getSearchMetaInfo($mutation, $stringToSearch);
		$sort = $order;
		if ($order === SortCreator::FULLTEXT) {
			$sort = $this->sortProvider->getBySearchMetaInfo($searchMetaInfo);
		}
		$bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($presenter, $mutation->pages->search, $state, $priceLevel, $sort, $parameters);
		$minScore = Repository::MINIMUM_SCORE;
		$bucketFilterBuilder->setSearchParameters($mutation->pages, $stringToSearch, $searchMetaInfo, $minScore);
		return $bucketFilterBuilder;
	}

	private function getSearchMetaInfo(Mutation $mutation, string $stringToSearch): SearchMetaInfo
	{
		$stringToSearch = FulltextData::normalizeString($stringToSearch);
		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		if ($esIndex === null) {
			return new SearchMetaInfo();
		}
		$b = new QueryBuilder();
		$query = $b->query()->bool()->addMust(
			(new IsPublic())->getCondition()
		);

		if ($mutation->pages->eshop !== null) {
			$query->addMust(
				(new IsInPath(
					$mutation->pages->eshop
				))->getCondition()
			);
		}

		$aggregations = [
			$b->aggregation()->filter(self::WRITER_TOTAL_MATCH)
				->setFilter(
					$b->query()->nested()->setPath('fulltext-writers')->setQuery(
						$b->query()->term(['fulltext-writers.name' => $stringToSearch])
					)
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::PRODUCT_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-name' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::PUBLISHER_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-publisher' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::ISBN_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-isbn' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::EAN_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-ean' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::CATEGORY_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-categories' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::TAG_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-tags' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::THEME_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-themes' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),
			$b->aggregation()->filter(self::SERIES_TOTAL_MATCH)
				->setFilter(
					$b->query()->term(['fulltext-seriesName' => $stringToSearch])
				)
				->addAggregation(
					$b->aggregation()->top_hits('hit')->setSort(['isBuyable' => 'desc', 'score' => 'desc'])->setSize(10000)->setSource(['_id', 'id', 'name']),
				),

		];

		$result = $this->esProductRepository->findByQuery($esIndex, $query, 0, aggregations: $aggregations);

		return new SearchMetaInfo(
			productNameTotalMatch: $this->getHitsByName($result, self::PRODUCT_TOTAL_MATCH),
			authorNameTotalMatch: $this->getHitsByName($result, self::WRITER_TOTAL_MATCH),
			publisherNameTotalMatch: $this->getHitsByName($result, self::PUBLISHER_TOTAL_MATCH),
			eanNameTotalMatch: $this->getHitsByName($result, self::EAN_TOTAL_MATCH),
			isbnNameTotalMatch: $this->getHitsByName($result, self::ISBN_TOTAL_MATCH),
			categoryNameTotalMatch: $this->getHitsByName($result, self::CATEGORY_TOTAL_MATCH),
			themeNameTotalMatch: $this->getHitsByName($result, self::THEME_TOTAL_MATCH),
			tagNameTotalMatch: $this->getHitsByName($result, self::TAG_TOTAL_MATCH),
			seriesNameTotalMatch: $this->getHitsByName($result, self::SERIES_TOTAL_MATCH),
		);
	}


	public function getHitsByName(ResultSet $result, string $topicName): array
	{
		return array_map(fn(array $hit) => $hit['_id'], $result->getAggregation($topicName)['hit']['hits']['hits']);
	}

}
