<?php declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Product;

use App\Model\ElasticSearch\All\Facade;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Messenger\Erp\ErpConsumer;
use App\Model\Messenger\Erp\ProductImage\ProductImageMessage;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\ImportCache\ImportCacheMapper;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationModel;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\ProductRedirect\ProductRedirect;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\TranslatorDB;
use App\PostType\Publisher\Model\Orm\PublisherLocalization\PublisherLocalizationModel;
use App\PostType\Series\Model\Orm\SeriesLocalization\SeriesLocalizationModel;
use App\PostType\Tag\Model\Orm\Tag\TagModel;
use App\PostType\Writer\Model\Orm\WriterLocalization\WriterLocalizationModel;
use App\Utils\DateTime;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\RecoverableMessageHandlingException;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
final class ProductConsumer extends ErpConsumer
{

	private const UID_TO_PARAMTER_TYPE = [
		'writer' => Parameter::TYPE_MULTISELECT,
		'publisher' => Parameter::TYPE_MULTISELECT,
		'jazyk' => Parameter::TYPE_MULTISELECT,
		'series' => Parameter::TYPE_MULTISELECT,
	];

	private ArrayHash $parameters;

	private array $metadata = [];

	private ?Product $parentProduct = null;

	private Mutation $defaultMutation;

	private ?Product $product = null;

	private ?ProductVariant $productVariant = null;

	public function __construct(
		protected readonly MutationsHolder $mutationsHolder,
		protected readonly MutationHolder $mutationHolder,
		protected readonly Orm $orm,
		protected readonly Facade $esAllFacade,
		protected readonly \App\Model\ElasticSearch\Product\Facade $esProductFacade,
		protected readonly \App\Model\ElasticSearch\Common\Facade $esCommonFacade,
		protected readonly ProductLocalizationModel $productLocalizationModel,
		protected readonly TranslatorDB $translator,
		protected readonly TagModel $tagModel,
		protected readonly ParameterValueModel $parameterValueModel,
		protected readonly PublisherLocalizationModel $publisherLocalizationModel,
		protected readonly WriterLocalizationModel $writerLocalizationModel,
		protected readonly SeriesLocalizationModel $seriesLocalizationModel,
		protected readonly ImportCacheMapper $importCacheMapper,
		protected readonly MessageBusInterface $messageBus,
	)
	{
	}

	public function __invoke(ProductMessage $message): void
	{
		$this->checkMessageAndImport($message, $this->orm);
	}

	protected function doImport(ImportCache $importCache): void
	{
		$importCache->status       = ImportCache::STATUS_IMPORTED;
		$importCache->message = 'ok';
		$importCache->importedTime = new DateTimeImmutable();

		$alreadyImported = true;
		try {
			$alreadyImported = $this->redirectCheck($importCache);
			$this->dataCheck($importCache);
		} catch (\Throwable $e) {
			$importCache->status  = ImportCache::STATUS_ERROR;
			$importCache->message = $e->getMessage();

		}

		if (!$alreadyImported) {
			try {
				$this->tryCatchQueryException(function () use ($importCache) {
					$this->product = $this->getProduct($importCache, $this->defaultMutation);

					$this->syncProduct($this->product, $importCache);
					$this->productVariant = $this->syncVariant($this->product, $importCache);

					// if parent is setted copy product, othervise sync with erp
					if ($this->parentProduct === null) {
						$productLocalization = $this->syncProductLocalization(
							$this->product,
							$this->defaultMutation,
							$importCache
						);
						$this->syncCategories($productLocalization, $importCache);
						$this->syncVariantLocalization($this->productVariant, $this->defaultMutation);
						$this->syncImages($importCache);
						$this->syncVersions($this->product, $importCache);
						$this->syncSeries($this->product, $importCache);

						$this->orm->product->persistAndFlush($this->product);
						$this->filterParameters($this->product, $importCache);
						$this->syncParameters($this->product, $this->defaultMutation);
						$isFreeTransport                = $this->tagModel->checkFreeTransport($this->product);
						$this->product->isFreeTransport = (int) $isFreeTransport;
						$this->tagModel->updateTagFreeTransport($this->product);

					} else {
						$productLocalization = $this->syncProductLocalization(
							$this->product,
							$this->defaultMutation,
							$importCache
						);
						$this->copyProduct(
							$this->product,
							$productLocalization,
							$this->productVariant,
							$this->parentProduct,
							$importCache
						);
					}
				});
				$importCache->status       = ImportCache::STATUS_IMPORTED;
				$importCache->message      = 'ok';
				$importCache->importedTime = new DateTimeImmutable();
			} catch (RecoverableMessageHandlingException $e) {
				throw $e;
			} catch (SkippedException $e) {
				$importCache->status = ImportCache::STATUS_SKIPPED;
				$importCache->importedTime = new DateTimeImmutable();
				$importCache->message = $e->getMessage();
			} catch (\Throwable $e) {
				$importCache->status  = ImportCache::STATUS_ERROR;
				$importCache->message = $e->getMessage();
			}
		}

		if ($this->hasWarning()) {
			$importCache->status  = ImportCache::STATUS_WARNING;
			$importCache->message = implode(' | ', $this->warnings);
		}

		$this->save($importCache);
	}

	private function createChecksum(ArrayHash $data): string
	{
		return md5(Json::encode($data));
	}

	private function setAsDeleted(int|string $extId): void
	{
		if (($product = $this->orm->product->getByExtId($extId)) !== null) {
			// deactivate product;
			$product->deleted = 1;
			$product->public = 0;
			$product->getLocalization($this->defaultMutation)->public = 0;

			$this->orm->product->persistAndFlush($product);
			$this->esProductFacade->updateAllMutationsNow($product);
			$this->esAllFacade->deleteNow($product);
		}
	}

	private function redirectCheck(ImportCache $importCache): bool
	{
		if ($importCache->data->deleted ?? false) {
			$this->setAsDeleted($importCache->data->id);
		}

		if (isset($importCache->data->code_redirect)) {
			//$this->setup();

			$productRedirect = $this->orm->product->getByExtCode($importCache->data->code_redirect);
			if ($productRedirect !== null) {
				try {
					$redirect          = new ProductRedirect();
					$redirect->erpId   = (int) $importCache->data->id;
					$redirect->product = $productRedirect;
					$this->orm->productRedirect->persistAndFlush($redirect);

					return true;
				} catch (UniqueConstraintViolationException $e) {
					throw new SkippedException('Item redirect already exists.');
				}
			} else {
				throw new SkippedException('Item with code "' . $importCache->data->code_redirect . '" for redirect not exists.');
			}
		}
		return false;
	}
	private function dataCheck(ImportCache $importCache): void
	{
		if ( ! isset($importCache->data->name) || $importCache->data->deleted) {
			$this->setAsDeleted($importCache->data->id);
			throw new SkippedException('Item "name" is null or item is deleted.');
		}

		if (strlen(trim($importCache->data->name)) === 0) {
			throw new SkippedException('Item "name" is empty.');
		}

		if ($this->orm->importCache->findBy([
				'type'   => $importCache->type,
				'status' => [ImportCache::STATUS_READY],
				'extId'  => $importCache->extId,
			])->countStored() > 0) {
			throw new SkippedException('Newer import pending.');
		}
	}
	private function getProduct(ImportCache $importCache, Mutation $mutation): Product
	{
		if (($product = $this->orm->product->getByExtId($importCache->data->id)) === null) {
			$product = new Product();
			$this->orm->product->attach($product);
			$product->internalName = $importCache->data->name;
			$product->extId = $importCache->data->id;
			$product->setMutation($mutation);
		}
		return $product;
	}

	private function syncProduct(Product $product, ImportCache $importCache): void
	{
		$product->internalName = $importCache->data->name;
		$product->syncTime = new DateTimeImmutable();
		$product->syncChecksum = $this->createChecksum($importCache->data);
		$product->itemType = $importCache->data->item_type ?? null;

		$productTypes = $this->orm->productType->findAll()->fetchPairs('uid');

		if ($product->itemType !== null) {
			$uid = Strings::webalize($product->itemType);
			if ( ! isset($productTypes[$uid])) {
				$pt       = new ProductType();
				$pt->uid  = $uid;
				$pt->name = $product->itemType;
				$pt->sort = count($productTypes) + 1;

				$this->orm->persist($pt);
				$productTypes[$uid] = $pt;
			}

			$product->productType = $productTypes[$uid];
		}

		$product->isElectronic = (int) ($importCache->data->electronic_product->is_electronic ?? false);
		$product->isOld = (int) ($importCache->data->is_sale_closed ?? false);
		//$product->isInPrepare = (int) ($importCache->data->is_upcoming ?? false);

		$product->isDamaged = (int) ($importCache->data->damaged->is_damaged ?? false);
		$product->damagedType = $product->isDamaged ? $importCache->data->damaged->type : null;

		if ($product->isDamaged) {
			$this->parentProduct = $this->orm->product->getByExtId($importCache->data->damaged->parent_product_id);
			$product->damagedParent = $this->parentProduct;

			if ($this->parentProduct === null) {
				throw new SkippedException('Skipped because damaged parent is null.');
			}

		} else {
			$product->damagedParent = $this->parentProduct = null;
		}

		$isBoosted = $importCache->data->boost->boosted ?? false;

		$product->boostScore = $isBoosted ? (int) $importCache->data->boost->boost_value : null;
		$product->boostScoreValid = $isBoosted ? new DateTimeImmutable($importCache->data->boost->date) : null;
		$product->erpScore = (int) ($importCache->data->score ?? 0);
		$product->erpHeurekaPopularity = (int) ($importCache->data->heureka_popularity ?? 0);
	}

	private function syncVariant(Product $product, ImportCache $importCache): ProductVariant
	{
		$variant = $product->variants->toCollection()->getBy(['extId' => $importCache->data->id]);
		if ($variant === null) {
			$variant = new ProductVariant();
		}

		$variant->code = $importCache->data->code;
		$variant->ean = $importCache->data->ean ?? '';
		$variant->isbn = $importCache->data->isbn;
		$variant->extId = $importCache->data->id;
		$variant->product = $product;

		return $variant;
	}

	private function syncProductLocalization(Product $product, Mutation $mutation, ImportCache $importCache): ProductLocalization
	{
		$defaultName = $product->internalName;
		$productLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $mutation]);
		$isNameChanged = Strings::webalize($defaultName) !== Strings::webalize($productLocalization?->name ?? '');
		if ($productLocalization === null) {

			$productLocalization = new ProductLocalization();
			$productLocalization->product = $product;
			$productLocalization->mutation = $mutation;

			$this->orm->productLocalization->persistAndFlush($productLocalization);

			$alias = $defaultName;

			$productLocalization->setAlias($alias);
			$isNameChanged = false;
		}

		$productLocalization->name = $defaultName;
		$productLocalization->nameAnchor = $defaultName;
		$productLocalization->nameTitle = $defaultName;
		$productLocalization->content = $importCache->data->description;
		$productLocalization->annotation = $importCache->data->slogan;

		//$active = (int) ($importCache->data->active ?? 1);
		//if ($active === 0 && count($importCache->data->serie) > 0) {
			//$active = 1;
		//	$product->isOld = 1;
		//}

		$productLocalization->public = 1; //$active;
		$this->orm->productLocalization->persistAndFlush($productLocalization);

		if ($isNameChanged) {
			$productLocalization->setAlias($defaultName);
		}

		return $productLocalization;
	}

	private function syncCategories(ProductLocalization $productLocalization, ImportCache $importCache): void
	{
		$categoryExtIds = [];
		foreach ($importCache->data->categories as $item) {
			$categoryExtIds[] = $item->id;
		}

		$categories = $this->orm->tree->findBy(['extId' => $categoryExtIds])->fetchPairs('id', 'id');

		if (count($categoryExtIds) !== count($categories)) {
			$this->addWarning('Some categories (' . count($categoryExtIds) . ') not found in DB (' . count($categories) . ').');
		}

		// add categories
		$this->productLocalizationModel->attachTo($productLocalization, array_keys($categories));
		$this->orm->persistAndFlush($productLocalization);
	}

	private function syncVariantLocalization(ProductVariant $variant, Mutation $mutation): void
	{
		$variantLocalization = $variant->getLocalization($mutation);
		if ($variantLocalization === null) {
			$variantLocalization = new ProductVariantLocalization();
			$variantLocalization->variant = $variant;
			$variantLocalization->mutation = $mutation;
			$variantLocalization->active = 1;
		}
	}

	private function syncImages(ImportCache $importCache): void
	{
		$data = [];
		$data['images'] = $importCache->data->images ?? [];
		$data['images_pages'] = $importCache->data->images_pages ?? [];

		if ($data['images'] === [] && $data['images_pages'] === []) {
			return;
		}

		$this->importCacheMapper->insert([
			[
				'type' => ImportCache::TYPE_PRODUCT_IMAGES,
				'status' => ImportCache::STATUS_READY,
				'extId' => $importCache->extId,
				'createdTime' => new DateTimeImmutable(),
				'data' => Json::encode($data),
			],
		]);
	}

	public function syncVersions(Product $product, ImportCache $importCache): void
	{
		foreach ($this->orm->productProduct->findBy(['type' => ProductProduct::TYPE_NORMAL, 'mainProduct' => $product]) as $attachedProduct) {
			$this->orm->productProduct->remove($attachedProduct);
		}
		$this->orm->productProduct->flush();

		if (isset($importCache->data->versions)) {
			$sort = 0;
			foreach ($importCache->data->versions as $code) {
				if (($productVersion = $this->orm->product->getByExtCode($code)) !== null) {
					$updated = $this->orm->productProduct->replace($product, $productVersion, ProductProduct::TYPE_NORMAL, $sort);
					$this->orm->productProduct->persist($updated);
					$sort++;
				}
			}
			$this->orm->flush();
		}
	}

	public function syncSeries(Product $product, ImportCache $importCache): void
	{
		$ids = [];
		if (isset($importCache->data->serie) && count($importCache->data->serie) > 0) {
			foreach ($importCache->data->serie as $importSeries) {
				if ($importSeries->name === null || $importSeries->name === '') {
					continue;
				}

				if (($seriesLocalization = $this->orm->seriesLocalization->getBy(['erpId' => (int) $importSeries->id])) === null) {
					continue;
				}

				$ids[(int) $importSeries->id] = (int) $importSeries->id;

				$this->orm->series->replace($seriesLocalization->series, $product, (int) $importSeries->position);
				$this->orm->persist($seriesLocalization->series);
			}
		}

		foreach ($product->series as $serie) {
			if (!isset($ids[$serie->getLocalization($this->defaultMutation)->erpId])) {
				$this->orm->series->removeProduct($serie, $product);
			}
		}
		$this->orm->flush();
	}

	private function filterParameters(Product $product, ImportCache $importCache): void
	{
		$parameters = (array) $importCache->data->parameters;

		// Typ produktu
		if (isset($importCache->data->item_type)) {
			$parameters[] = [
				'uid' => 'typ-produktu',
				'title' => 'Typ produktu',
				'value' => $importCache->data->item_type,
			];
		}

		// Autori
		if (isset($importCache->data->authors)) {
			$valuesAuthor = [];
			foreach ($importCache->data->authors as $author) {
				$valuesAuthor[] = $author['full_name'];
				//$this->metadata['writer']['lastNameFirstLetter'][$author['full_name']] = Strings::substring($author['second_name'], 0, 1);
				$this->metadata['writer']['firstName'][$author['full_name']] = $author['name'] ?? '';
				$this->metadata['writer']['lastName'][$author['full_name']] = $author['sname'] ?? '';
				$this->metadata['writer']['erpId'][$author['full_name']] = isset($author['id']) ? (int) $author['id'] : null;
			}

			if ($valuesAuthor !== []) {
				$parameters[] = [
					'uid' => 'writer',
					'title' => 'Autor',
					'value' => $valuesAuthor,
				];
			}

		}

		if (isset($importCache->data->serie)) {
			$valuesSeries = [];
			foreach ($importCache->data->serie as $serie) {
				$this->metadata['series']['erpId'][$serie['name']] = isset($serie['id']) ? (int) $serie['id'] : null;
				$valuesSeries[] = $serie['name'];
			}

			if ($valuesSeries !== []) {
				$parameters[] = [
					'uid' => 'series',
					'title' => 'Série',
					'value' => $valuesSeries,
				];
			}
		}

		// Nakladatelstvi
		if (($publisher = $this->getImportedParameterById('pub', $parameters, true)) !== null) {
			$this->metadata['publisher']['erpId'][$publisher['value']] = isset($importCache->data->publisher_id) ? (int) $importCache->data->publisher_id : null;
			$parameters[] = [
				'uid' => 'publisher',
				'title' => 'Nakladatelství',
				'value' => $publisher['value'],
			];
		}

		// Ak neexistuje parameter Váha
		if ($this->getImportedParameterById('5', $parameters) === null) {
			$parameters[] = [
				'uid' => 'vaha',
				'title' => 'Váha',
				'value' => '500',
			];
		}

		// Test datumu vydani a ak je nevalidni hodnota, urobit default
		if (($date_published = $this->getImportedParameterById('600', $parameters, true)) !== null) {
			try {
				$date_published['value'] = str_replace(' ', '', $date_published['value']);
				$value = DateTime::from($date_published['value'])->format('d.m.Y');
			} catch (\Throwable $e) {
				$value = '01.01.' . date('Y');
			}

			$parameters[] = [
				'uid' => Parameter::UID_PUBLISH_DATE,
				'title' => 'Datum vydání',
				'value' => $value,
			];
		}

		$this->parameters = ArrayHash::from($parameters);
	}

	private function getImportedParameterById(string|int $id, array &$parameters, bool $removeFromParameters = false): ?array
	{
		foreach ($parameters as $key => $parameter) {
			if (isset($parameter['id']) && $parameter['id'] === $id) {
				if ($removeFromParameters) {
					unset($parameters[$key]);
				}
				return (array) $parameter;
			}
		}
		return null;
	}

	private function syncParameters(Product $product, Mutation $mutation): void
	{
		$addedValues = [];
		foreach ($this->parameters as $prm) {

			$name = strlen(trim($prm->title)) > 0 ? $prm->title : ($prm->id ?? 'unknown');

			$uid           = $prm->uid ?? Strings::webalize($name);
			$isMultiselect = is_array($prm->value) || $prm->value instanceof ArrayHash;

			if (($paramEntity = $this->orm->parameter->getBy(['uid' => $uid])) === null) {
				$isBaseParameter = isset(self::UID_TO_PARAMTER_TYPE[$uid]);

				$paramEntity              = new Parameter();
				$paramEntity->injectTranslatorDB($this->translator);
				$paramEntity->uid         = $uid;
				$paramEntity->name        = Strings::firstUpper(Strings::lower($name));
				$paramEntity->type        = $isMultiselect ? Parameter::TYPE_MULTISELECT : (self::UID_TO_PARAMTER_TYPE[$uid] ?? Parameter::TYPE_TEXT);
				$paramEntity->isProtected = $isBaseParameter;
				$paramEntity->isInFilter  = (int) $isBaseParameter;
			}

			$paramEntity->injectTranslatorDB($this->translator);

			$this->orm->parameter->persist($paramEntity);

			$paramValues = [$prm->value];
			if ($isMultiselect) {
				$paramValues = $prm->value;
			}

			foreach ($paramValues as $value) {

				$isParameterValueCreated = null;
				$parameterValueEntity    = $this->parameterValueModel->attachParameterValue($product, $uid, $value, $isParameterValueCreated);
				$addedValues[]           = $parameterValueEntity->id;
				if ($isParameterValueCreated === true) {
					$this->createPostType($paramEntity, $parameterValueEntity, $mutation);
				}
			}
		}

		// remove unused values
		foreach ($product->parametersValues->toCollection()->findBy(['id!=' => $addedValues]) as $parameterValue) {
			$this->orm->product->removeParameterValue($product, $parameterValue);
		}
	}

	private function createPostType(Parameter $parameter, ParameterValue $parameterValue, Mutation $mutation): void
	{
		if ($parameter->uid === 'publisher') {
			$object = $this->publisherLocalizationModel->create(
				mutation: $mutation,
				name: $parameterValue->internalValue,
				parameterValue: $parameterValue,
				erpId: $this->metadata['publisher']['erpId'][$parameterValue->internalValue] ?? null,
			);
		} elseif ($parameter->uid === 'writer') {
			$object = $this->writerLocalizationModel->create(
				mutation: $mutation,
				name: $parameterValue->internalValue,
				parameterValue: $parameterValue,
				//lastNameFirstLetter: $this->metadata['writer']['lastNameFirstLetter'][$parameterValue->internalValue] ?? null,
				erpId: $this->metadata['writer']['erpId'][$parameterValue->internalValue] ?? null,
				firstName: $this->metadata['writer']['firstName'][$parameterValue->internalValue] ?? null,
				lastName: $this->metadata['writer']['lastName'][$parameterValue->internalValue] ?? null,
			);
		} elseif ($parameter->uid === 'series') {
			$object = $this->seriesLocalizationModel->create(
				mutation: $mutation,
				name: $parameterValue->internalValue,
				parameterValue: $parameterValue,
				erpId: $this->metadata['series']['erpId'][$parameterValue->internalValue] ?? null,
				uid: Strings::webalize($parameterValue->internalValue),
			);
		}

		if (!isset($object)) {
			return;
		}

		$this->esCommonFacade->update($object, $this->defaultMutation);
	}

	private function copyProduct(Product $product, ProductLocalization $productLocalization, ProductVariant $productVariant, Product $parent, ImportCache $importCache): void
	{
		// sync categories
		$parentCategories = $parent->attachCategoriesAll->fetchPairs(null, 'id');
		$this->productLocalizationModel->attachTo($productLocalization, $parentCategories);

		// sync variant localization
		$this->syncVariantLocalization($productVariant, $product->getMutation());

		// sync images
		if ($parent->images->countStored() === 0) {
			// from import
			$parentImportImageCache = $this->orm->importCache->findBy(['type' => ImportCache::TYPE_PRODUCT_IMAGES, 'extId' => $parent->extId])->fetch();

			if ($parentImportImageCache !== null) {
				$ic              = new ImportCache();
				$ic->type        = ImportCache::TYPE_PRODUCT_IMAGES;
				$ic->status      = ImportCache::STATUS_READY;
				$ic->createdTime = new DateTimeImmutable();
				$ic->extId       = $product->extId;
				$ic->data        = $parentImportImageCache->data;

				$this->orm->importCache->persistAndFlush($ic);
			}
		} else {
			$productImages = $productImagesToDelete = $product->images->toCollection()->fetchPairs('libraryImage->id');
			$order = 1;
			foreach ($parent->images as $parentImage) {
				if (!isset($productImages[$parentImage->libraryImage->id])) {
					$productImage = new ProductImage();
					$this->orm->productImage->attach($productImage);
					$productImage->libraryImage = $parentImage->libraryImage->id;
				} else {
					/** @var ProductImage $productImage */
					$productImage = $productImages[$parentImage->libraryImage->id];
					unset($productImagesToDelete[$parentImage->libraryImage->id]);
				}

				$productImage->product = $product;
				$productImage->sort = $order;

				$order++;
				$this->orm->persist($productImage);
			}

			foreach ($productImagesToDelete as $productImageToDelete) {
				$this->orm->remove($productImageToDelete);
			}
		}

		// sync versions

		foreach ($this->orm->productProduct->findBy(['type' => ProductProduct::TYPE_NORMAL, 'mainProduct' => $product]) as $attachedProduct) {
			$this->orm->productProduct->remove($attachedProduct);
		}
		$this->orm->productProduct->flush();

		$sort = 0;
		foreach ($parent->productsAll as $relatedProduct) {
			$updated = $this->orm->productProduct->replace($product, $relatedProduct, ProductProduct::TYPE_NORMAL, $sort);
			$this->orm->productProduct->persist($updated);
		}

		$this->orm->product->persistAndFlush($product);

		// sync parameters
		$addedValues = [];
		foreach ($parent->parametersValues as $parameterValue) {

			$parameterValueEntity    = $this->parameterValueModel->attachParameterValue($product, $parameterValue->parameter->uid, $parameterValue->internalValue);
			$addedValues[] = $parameterValueEntity->id;
		}

		foreach ($product->parametersValues->toCollection()->findBy(['id!=' => $addedValues]) as $parameterValue) {
			$this->orm->product->removeParameterValue($product, $parameterValue);
		}

		//$this->orm->persistAndFlush($product);
	}

	protected function setup(): void
	{
		$this->orm->reconnect();

		$this->defaultMutation = $this->mutationsHolder->getDefault();

		$this->mutationHolder->setMutation($this->defaultMutation);
		$this->orm->setMutation($this->defaultMutation);
		$this->orm->setPublicOnly(false);

		$this->translator->setMutation($this->defaultMutation);

		$this->parameters = new ArrayHash();
		$this->metadata = [];
		$this->parentProduct = null;
	}

	private function save(ImportCache $importCache): void
	{
		$this->productVariant?->flushCache();

		if ($this->product !== null) {
			$this->orm->persistAndFlush($this->product);
			$this->product->flushCache();

			// performance - send update to messenger
			$this->esProductFacade->updateOrDeleteAllMutations($this->product);
			$this->esAllFacade->update($this->product);
		}

		$this->orm->persistAndFlush($importCache);

		if ($importCache->status === ImportCache::STATUS_IMPORTED) {
			$productImageImportCache = $this->orm->importCache->getBy([
				'type'   => ImportCache::TYPE_PRODUCT_IMAGES,
				'status' => ImportCache::STATUS_READY,
				'extId'  => $importCache->extId,
			]);

			if ($productImageImportCache !== null) {
				$productImageImportCache->status = ImportCache::STATUS_QUEUED;
				$this->orm->persistAndFlush($productImageImportCache);
				$this->messageBus->dispatch(new ProductImageMessage($productImageImportCache->id));
			}

			foreach ($this->orm->importCache->findBy(['type' => [ImportCache::TYPE_PRICE, ImportCache::TYPE_STOCK], 'status' => ImportCache::STATUS_NEW, 'extId' => $importCache->extId]) as $ic) {
				$ic->status = ImportCache::STATUS_QUEUED;
				$this->orm->importCache->persistAndFlush($ic);
				$messageClass = ImportCache::toMessageClass($ic->type);
				$this->messageBus->dispatch(new $messageClass($ic->id));
			}
		}
	}

}
