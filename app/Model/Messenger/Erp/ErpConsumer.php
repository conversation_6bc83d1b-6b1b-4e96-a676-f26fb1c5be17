<?php
declare(strict_types=1);

namespace App\Model\Messenger\Erp;

use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Orm;

abstract class ErpConsumer
{

	use InitImportCache;

	protected array $signals = [];

	protected array $warnings = [];

	abstract protected function setup(): void;
	protected function checkMessageAndImport(ImportMessage $importMessage, Orm $orm): void
	{
		$this->signals = $importMessage->getSignals();
		$this->warnings = [];
		$this->setup();

		$importCache = $this->initImportCache($importMessage, $orm);
		if ($importCache !== null) {
			// check for newer imports
			$hasNewer = $orm->importCache->findBy(['type' => $importCache->type, 'status' => [ImportCache::STATUS_QUEUED], 'id>' => $importCache->id])->countStored();
			if ($hasNewer) {
				$importCache->status = ImportCache::STATUS_WARNING;
				$importCache->message = 'Newer import pending. Skipping. (' . $hasNewer . ' newer imports)';
				$orm->importCache->persistAndFlush($importCache);
			} else {
				$this->doImport($importCache);
			}
		}
	}
	abstract protected function doImport(ImportCache $importCache): void;

	protected function hasSignal(ImportMessageSignal $importMessageSignal): bool
	{
		return in_array($importMessageSignal, $this->signals);
	}

	protected function addWarning(string $message): void
	{
		$this->warnings[] = $message;
	}

	protected function hasWarning(): bool
	{
		return count($this->warnings) > 0;
	}

}
