<?php declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Order;

use App\Model\Messenger\Erp\ErpConsumer;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\DeliveryMethod\DeliveryMethodRegistry;
use App\Model\Orm\DeliveryMethod\Legacy;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Delivery\LegacyDeliveryInformation;
use App\Model\Orm\Order\Delivery\OrderDelivery;
use App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation;
use App\Model\Orm\Order\Delivery\PickupDeliveryInformation;
use App\Model\Orm\Order\Delivery\StoreDeliveryInformation;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\NumberSequence\OrderNumberGenerator;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\OrderStateHistory\OrderStateChange;
use App\Model\Orm\Order\Payment\BankTransferPaymentInformation;
use App\Model\Orm\Order\Payment\LegacyPaymentInformation;
use App\Model\Orm\Order\Payment\OrderPayment;
use App\Model\Orm\Order\Payment\PaymentState;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Voucher\Voucher;
use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Brick\Money\Currency;
use Brick\Money\Money;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\IEntity;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Tracy\Debugger;

#[AsMessageHandler]
final class OrderImportConsumer extends ErpConsumer
{

	private ?Order $order = null;

	private Mutation $defaultMutation;

	private int $defaultTax = 21;

	private int $defaultCertificateTax = 0;

	private int $defaultCouponTax = 0;

	private LoggerInterface $logger;

	public function __construct(
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly DeliveryMethodRegistry $deliveryMethodRegistry,
		private readonly LoggerManager $loggerManager,
		private readonly OrderNumberGenerator $orderNumberGenerator,
	)
	{
	}

	protected function setup(): void
	{
		$this->orm->reconnect();

		$this->defaultMutation = $this->mutationsHolder->getDefault();

		$this->mutationHolder->setMutation($this->defaultMutation);
		$this->orm->setMutation($this->defaultMutation);
		$this->orm->setPublicOnly(false);

		$this->logger = $this->loggerManager->get('order_process');
	}

	public function __invoke(OrderImportMessage $orderMessage): void
	{
		$this->checkMessageAndImport($orderMessage, $this->orm);
	}

	protected function doImport(ImportCache $importCache): void
	{
		$this->logger->debug('Starting consume order with id: ' . $importCache->id, ['consumer_name' => $_ENV['MESSENGER_CONSUMER_NAME'] ?? null]);
		try {
			$delayedOrder = false;
			$this->order = $this->orm->order->getBy(['extId' => $importCache->extId, 'orderNumber!=' => null]);

			if ($this->order === null) {
				$this->order = $this->orm->order->getBy(['orderNumber' => $importCache->data->number]);
				if ($this->order !== null) {
					$delayedOrder = true;
				}
			}

			if ($this->order === null) {
				$user = $this->orm->user->getByEmail($importCache->data->customer->email, $this->defaultMutation);
				$country = $this->orm->state->getBy(['code' => Strings::upper($importCache->data->customer->invoice_address->country_code)]);

				$this->order = new Order(
					mutation: $this->defaultMutation,
					priceLevel: $this->orm->priceLevel->getDefault(),
					country: $country,
					user: $user,
					currency: Currency::of($importCache->data->currency),
				);
				$this->order->setReadOnlyValue('cookieHash', null);
				$this->order->extId = $importCache->extId;
				$this->order->hash = 'erp' . bin2hex(random_bytes(17));
				$this->orm->persistAndFlush($this->order);

				$this->order->orderNumber = $importCache->data->number;
				$this->order->placedAt = new DateTimeImmutable($importCache->data->date);
			}

			if ($delayedOrder) {
				$this->order->extId = $importCache->extId;
				$this->order->syncedAt = new DateTimeImmutable();
				$this->order->hash = 'erp' . bin2hex(random_bytes(17));
				$this->order->orderNumber = $importCache->data->number;
				$this->order->placedAt = new DateTimeImmutable($importCache->data->date);

				$this->orm->persistAndFlush($this->order);
			}

			$stateFromErp = OrderState::fromErp($importCache->data->state);
			if ($this->order->stateChanges->toCollection()->findBy(['to' => $stateFromErp])->countStored() === 0) {
				$this->order->stateChanges->add(OrderStateChange::of($this->order, $this->order->state, $stateFromErp));
				$this->order->state = $stateFromErp;
			}

			$this->order->note = $importCache->data->customer_note ?? '';
			$this->order->email = $importCache->data->customer->email;
			$this->order->phone = $importCache->data->customer->phone;
			$this->order->name = $importCache->data->customer->name;

			$this->order->street = $importCache->data->customer->invoice_address->street ?? '';
			$this->order->city = $importCache->data->customer->invoice_address->city ?? '';
			$this->order->zip = $importCache->data->customer->invoice_address->zip_code ?? '';
			$this->order->companyName = $importCache->data->customer->invoice_address->company;
			$this->order->companyIdentifier = $importCache->data->customer->invoice_address->ic;
			$this->order->vatNumber = $importCache->data->customer->invoice_address->dic;

			$this->order->extState = $importCache->data->state_title;

			//$this->order->syncedAt = new DateTimeImmutable();
			$this->order->syncTime = new DateTimeImmutable();
			$this->order->syncChecksum = md5(Json::encode($importCache->data));

			$this->importDelivery($importCache);
			$this->importPayment($importCache);
			$this->importProductItems($importCache);
			$this->importGiftItems($importCache);
			$this->importVoucherItems($importCache);
			$this->importCertificateItems($importCache);
			$this->importInvoices($importCache);
			$this->importPromotions($importCache);

			$pricesSame = $this->order->getTotalPriceVat(true)->isEqualTo(Money::of($importCache->data->price_in_cart_with_tax_with_discounts, $importCache->data->currency));

			$importCache->status = ImportCache::STATUS_IMPORTED;
			$importCache->message = 'ok';
			if (!$pricesSame) {
				$importCache->status = ImportCache::STATUS_WARNING;
				$importCache->message = 'Prices are not equal. Import price: ' . $importCache->data->price_in_cart_with_tax_with_discounts . ' | Eshop price: ' . $this->order->getTotalPriceVat(true)->getAmount()->toFloat();
			}
			$importCache->importedTime = new DateTimeImmutable();
		} catch (\Throwable $e) {
			Debugger::log($e);
			$importCache->status = ImportCache::STATUS_ERROR;
			$importCache->message = $e->getMessage();
		}
		$this->logger->debug('Finished consume order with id: ' . $importCache->id, ['consumer_name' => $_ENV['MESSENGER_CONSUMER_NAME'] ?? null]);
		$this->save($importCache);
	}

	private function importDelivery(ImportCache $importCache): void
	{
		$deliveryMethodByExtCode = $this->orm->deliveryMethodPrice->getBy(['externalId' => $importCache->data->delivery->external_id]);
		if ($deliveryMethodByExtCode !== null) {
			$deliveryMethod = $deliveryMethodByExtCode->deliveryMethod;
		} else {
			$deliveryMethod = $this->orm->deliveryMethod->getBy(['deliveryMethodUniqueIdentifier' => Legacy::ID]);
		}

		$vatRateValue = BigDecimal::of($importCache->data->delivery->tax);
		$vatRate = $this->createVatRate($vatRateValue);

		if (isset($importCache->data->delivery->valueNoTax)) {
			$price = $this->createPrice($importCache->data->delivery->valueNoTax);
		} else {
			$price = $this->createPrice(VatCalculator::priceWithoutVat(Money::of($importCache->data->delivery->value, $this->order->currency, roundingMode: RoundingMode::HALF_UP), $vatRateValue));
		}

		$data = [
			'order' => $this->order,
			'deliveryMethod' => $deliveryMethod,
			'deliveryMethodName' => $importCache->data->delivery->title ?? null,
			'vatRate' => $vatRate ,
			'vatRateValue' => $vatRateValue ,
			'unitPrice' => $price,
		];

		if ($this->order->delivery === null) {
			$delivery = OrderDelivery::createFromErp(...array_values($data));
			$this->order->setDelivery($delivery, true);
		} else {
			if ($deliveryMethod->getDeliveryMethod()->getDeliveryType() !== $this->order->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()) {
				$oldDelivery = $this->order->delivery;
				$newDelivery = OrderDelivery::createFromErp(...array_values($data));
				$this->order->setDelivery($newDelivery, true);
				$this->orm->order->persistAndFlush($this->order);
				$this->orm->orderDelivery->removeAndFlush($oldDelivery);
			}
			foreach ($data as $key => $value) {
				$this->order->delivery->{$key} = $value;
			}
		}
		$country = $this->orm->state->getBy(['code' => Strings::upper($importCache->data->customer->delivery_address->country_code)]);

		$informationData = [
			'city' => $importCache->data->customer->delivery_address->city ?? '',
			'country' => $country,
			'zip' => $importCache->data->customer->delivery_address->zip_code ?? '',
			'street' => $importCache->data->customer->delivery_address->street ?? '',
			'company' => $importCache->data->customer->delivery_address->company ?? '',
			'name' => $importCache->data->customer->name ?? null,
			'trackingCode' => $importCache->data->delivery->pack_number ?? null,
			'trackingUrl' => $importCache->data->delivery->pack_tracking_link ?? null,
			'phoneNumber' => $importCache->data->customer->phone ?? '',
			'pickupPointId' => $importCache->data->delivery->pickup_point_id ?? null, // CHECK
		];

		if ($this->order->delivery->information === null) {
			$informationClass = $deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
			$this->order->delivery->information = new $informationClass($this->order->delivery);
		}

		$this->order->delivery->information->externalId = $importCache->data->delivery->external_id;

		if ($this->order->delivery->information instanceof LegacyDeliveryInformation) {
			$information = $this->order->delivery->information;
			assert($information instanceof LegacyDeliveryInformation);
			$this->assignProperty($information, $informationData);
		} elseif ($this->order->delivery->information instanceof StoreDeliveryInformation) {
			$information = $this->order->delivery->information;
			assert($information instanceof StoreDeliveryInformation);
			$information->phoneNumber = $informationData['phoneNumber'];
		} elseif ($this->order->delivery->information instanceof PhysicalDeliveryInformation) {
			$information = $this->order->delivery->information;
			assert($information instanceof PhysicalDeliveryInformation);
			unset($informationData['pickupPointId']);
			$this->assignProperty($information, $informationData);
		} elseif ($this->order->delivery->information instanceof PickupDeliveryInformation) {
			$information = $this->order->delivery->information;
			assert($information instanceof PickupDeliveryInformation);
			$information->phoneNumber = $informationData['phoneNumber'];
			$information->pickupPointId = $informationData['pickupPointId'];
			$information->trackingCode = $informationData['trackingCode'];
			$information->trackingUrl = $informationData['trackingUrl'];
		}
	}

	private function importPayment(ImportCache $importCache): void
	{
		$paymentMethodByExtCode = $this->orm->paymentMethodPrice->getBy(['externalId' => $importCache->data->payment->external_id]);
		if ($paymentMethodByExtCode !== null) {
			$paymentMethod = $paymentMethodByExtCode->paymentMethod;
		} else {
			$paymentMethod = $this->orm->paymentMethod->getBy(['paymentMethodUniqueIdentifier' => \App\Model\Orm\PaymentMethod\Legacy::ID]);
		}

		$vatRateValue = BigDecimal::of($importCache->data->payment->tax);
		$vatRate      = $this->createVatRate($vatRateValue);

		if (isset($importCache->data->payment->valueNoTax)) {
			$price = $this->createPrice($importCache->data->payment->valueNoTax);
		} else {
			$price = $this->createPrice(VatCalculator::priceWithoutVat(Money::of($importCache->data->payment->value, $this->order->currency, roundingMode: RoundingMode::HALF_UP), $vatRateValue));
		}

		$data = [
			'order'             => $this->order,
			'paymentMethod'     => $paymentMethod,
			'paymentMethodName' => $importCache->data->payment->title ?? null,
			'vatRate'           => $vatRate,
			'vatRateValue'      => $vatRateValue,
			'unitPrice'         => $price,
		];

		if ($this->order->payment === null) {
			$payment = OrderPayment::createFromErp(...array_values($data));
			$this->order->setPayment($payment);
		} else {
			if ($paymentMethod->getPaymentMethod()->getPaymentType() !== $this->order->payment->paymentMethod->getPaymentMethod()->getPaymentType()) {
				$oldPayment = $this->order->payment;
				$newPayment = OrderPayment::createFromErp(...array_values($data));
				$this->order->setPayment($newPayment);
				$this->orm->order->persistAndFlush($this->order);
				$this->orm->orderPayment->removeAndFlush($oldPayment);
			}

			foreach ($data as $key => $value) {
				$this->order->payment->{$key} = $value;
			}
		}

		if ($this->order->payment->information === null) {
			$informationClass = $paymentMethod->getPaymentMethod()->getPaymentType()->getEntityClassName();
			$this->order->payment->information = new $informationClass($this->order->payment);
		}

		$this->order->payment->information->externalId = $importCache->data->payment->external_id;

		if ($this->order->payment->information instanceof LegacyPaymentInformation) {
			$information = $this->order->payment->information;
			assert($information instanceof LegacyPaymentInformation);
			$this->assignProperty($information, ['variableSymbol' => $importCache->data->payment->payment_variable_symbol ?? null]);

		} elseif ($this->order->payment->information instanceof BankTransferPaymentInformation) {
			$information = $this->order->payment->information;
			assert($information instanceof BankTransferPaymentInformation);
			if ($importCache->data->payment->state === 'Success') {
				$information->addState(PaymentState::Completed);
			}
			$this->assignProperty($information, ['variableSymbol' => $importCache->data->payment->payment_variable_symbol ?? null]);
		} /* elseif ($this->order->payment->information instanceof CardPaymentInformation) {
			// TODO;
		} elseif ($this->order->payment->information instanceof CashOnDeliveryPaymentInformation) {
			// TODO;
		} elseif ($this->order->payment->information instanceof CertificatePaymentInformation) {
			// TODO;
		} elseif ($this->order->payment->information instanceof InvoicePaymentInformation) {
			// TODO;
		} */
	}

	private function importProductItems(ImportCache $importCache): void
	{
		$products = $this->order->products->toCollection()->fetchAll();

		foreach ((array) $importCache->data->products as $index => $product) {
			$variant = $this->orm->productVariant->getBy(['code' => $product->code]);
			$vatRateValue = BigDecimal::of($product->tax);

			if (isset($product->priceNoTax)) {
				$price = $this->createPrice($product->priceNoTax);
			} else {
				$taxPrice = Price::from(Money::of(amount: $product->price, currency: $this->order->currency, roundingMode: RoundingMode::HALF_UP))->asMoney(4);
				$notaxPrice = VatCalculator::priceWithoutVat($taxPrice, $vatRateValue);
				$price = $this->createPrice($notaxPrice);
			}

			$data = [
				'order' => $this->order,
				'variant' => $variant,
				'variantName' => $product->name,
				'variantCode' => $product->code,
				'unitPrice' => $price,
				'vatRate' => $this->createVatRate($vatRateValue),
				'vatRateValue' => $vatRateValue,
				'amount' => $product->count,
			];

			if (isset($products[$index])) {
				$productEntity = $products[$index];
				foreach ($data as $key => $value) {
					$productEntity->{$key} = $value;
				}
				unset($products[$index]);
			} else {
				$productItem = ProductItem::createFromErp(...array_values($data));
				$this->order->products->add($productItem);
			}

		}

		if ($products !== []) {
			foreach ($products as $product) {
				$this->order->products->remove($product);
				$this->orm->orderProduct->remove($product);
			}
		}
	}

	private function importPromotions(ImportCache $importCache): void
	{
		foreach ($this->order->promotions as $promotion) {
			$this->order->promotions->remove($promotion);
			$this->orm->orderPromotion->remove($promotion);
		}
	}
	private function importGiftItems(ImportCache $importCache): void
	{
		$gifts = $this->order->gifts->toCollection()->fetchAll();

		foreach ((array) $importCache->data->gifts as $index => $gift) {
			$giftLocalization = $this->orm->giftLocalization->getById($gift->id ?? null);
			$vatRateValue = BigDecimal::of($gift->tax ?? $this->defaultTax);

			if (isset($gift->priceNoTax)) {
				$price = $this->createPrice($gift->priceNoTax);
			} else {
				$taxPrice = Price::from(Money::of(amount: $gift->price ?? 0, currency: $this->order->currency, roundingMode: RoundingMode::HALF_UP))->asMoney(4);
				$notaxPrice = VatCalculator::priceWithoutVat($taxPrice, $vatRateValue);
				$price = $this->createPrice($notaxPrice);
			}

			$data = [
				'order' => $this->order,
				'giftLocalization' => $giftLocalization,
				'giftName' => $gift->name ?? null,
				'unitPrice' => $price,
				'vatRate' => $this->createVatRate($vatRateValue),
				'vatRateValue' => $vatRateValue,
				'amount' => $gift->count ?? 1,
			];

			if (isset($gifts[$index])) {
				$giftEntity = $gifts[$index];
				foreach ($data as $key => $value) {
					$giftEntity->{$key} = $value;
				}

				unset($gifts[$index]);
			} else {
				$giftItem = GiftItem::createFromErp(...array_values($data));
				$this->order->gifts->add($giftItem);
			}
		}

		if ($gifts !== []) {
			foreach ($gifts as $gift) {
				$this->order->gifts->remove($gift);
				$this->orm->orderGift->remove($gift);
			}
		}
	}

	private function importVoucherItems(ImportCache $importCache): void
	{
		$voucherItem = $this->order->getAppliedVoucherItem();
		if (isset($importCache->data->coupon) && (array) $importCache->data->coupon !== []) {
			$coupon = $importCache->data->coupon;

			$voucherCode = $this->orm->voucherCode->getById($coupon->id ?? null);
			if (isset($coupon->code)) {
				$voucherCode = $this->orm->voucherCode->getByCode($coupon->code, $this->defaultMutation);
			}
			$vatRateValue = BigDecimal::of($coupon->tax ?? $this->defaultCouponTax);

			foreach (['valueNoTax', 'value'] as $key) {
				if (isset($coupon->{$key}) && $coupon->{$key} > 0) {
					$coupon->{$key} = $coupon->{$key} * -1;
				}
			}

			if (isset($coupon->valueNoTax)) {
				$price = $this->createPrice($coupon->valueNoTax);
			} else {
				$taxPrice = Price::from(Money::of(amount: $coupon->value ?? 0, currency: $this->order->currency, roundingMode: RoundingMode::HALF_UP))->asMoney(4);
				$notaxPrice = VatCalculator::priceWithoutVat($taxPrice, $vatRateValue);
				$price = $this->createPrice($notaxPrice);
			}

			$data = [
				'order' => $this->order,
				'voucherCode' => $voucherCode,
				'voucherName' => $coupon->name ?? 'Slevový kupón',
				'voucherCodeString' => $coupon->code ?? null,
				'voucherType' => Voucher::TYPE_AMOUNT,
				'unitPrice' => $price,
				'vatRate' => $this->createVatRate($vatRateValue),
				'vatRateValue' => $vatRateValue,
				'amount' => $coupon->count ?? 1,
			];

			if ($voucherItem !== null) {
				foreach ($data as $key => $value) {
					$voucherItem->{$key} = $value;
				}
			} else {
				$voucherItem = VoucherItem::createFromErp(...array_values($data));
				$this->order->vouchers->add($voucherItem);
			}

		} elseif ($voucherItem !== null) {
			foreach ($this->order->vouchers as $voucher) {
				$this->order->vouchers->remove($voucher);
				$this->orm->orderVoucher->remove($voucher);
			}

		}
	}

	private function importCertificateItems(ImportCache $importCache): void
	{
		if (isset($importCache->data->certificates) && (array) $importCache->data->certificates !== []) {
			/** @var VoucherItem[] $certificates */
			$certificates = $this->order->getAppliedGiftVouchers()->fetchAll();
			foreach ((array) $importCache->data->certificates as $index => $certificate) {
				$voucherCode = $this->orm->voucherCode->getById($certificate->id ?? null);
				if (isset($certificate->code)) {
					$voucherCode = $this->orm->voucherCode->getByCode($certificate->code, $this->defaultMutation);
				}
				$vatRateValue = BigDecimal::of($certificate->tax ?? $this->defaultCertificateTax);

				foreach (['valueNoTax', 'value'] as $key) {
					if (isset($certificate->{$key}) && $certificate->{$key} > 0) {
						$certificate->{$key} = $certificate->{$key} * -1;
					}
				}

				if (isset($certificate->valueNoTax)) {
					$price = $this->createPrice($certificate->valueNoTax);
				} else {
					$taxPrice = Price::from(Money::of(amount: $certificate->value ?? 0, currency: $this->order->currency, roundingMode: RoundingMode::HALF_UP))->asMoney(4);
					$notaxPrice = VatCalculator::priceWithoutVat($taxPrice, $vatRateValue);
					$price = $this->createPrice($notaxPrice);
				}

				$data = [
					'order' => $this->order,
					'voucherCode' => $voucherCode,
					'voucherName' => $certificate->name ?? 'Dárkový certifikát',
					'voucherCodeString' => $certificate->code ?? null,
					'voucherType' => Voucher::TYPE_AMOUNT_COMBINATION,
					'unitPrice' => $price,
					'vatRate' => $this->createVatRate($vatRateValue),
					'vatRateValue' => $vatRateValue,
					'amount' => $certificate->count ?? 1,
				];

				if (isset($certificates[$index])) {
					$voucherEntity = $certificates[$index];
					foreach ($data as $key => $value) {
						$voucherEntity->{$key} = $value;
					}

					unset($certificates[$index]);
				} else {
					$voucherItem = VoucherItem::createFromErp(...array_values($data));
					$this->order->vouchers->add($voucherItem);
				}
			}

			if ($certificates !== []) {
				foreach ($certificates as $certificate) {
					$this->order->vouchers->remove($certificate);
					$this->orm->orderVoucher->remove($certificate);
				}
			}
		}
	}
	private function save(ImportCache $importCache): void
	{
		if ($this->order !== null) {
			$this->orm->persistAndFlush($this->order);
		}

		//if ($this->hasSignal(ImportMessageSignal::Last)) {
		//	$lastOrder = $this->orm->order->getLastPlacedOrder();
		//	if ($lastOrder !== null) {
		//		$this->orderNumberGenerator->updateSequence($lastOrder);
		//	}
		//}

		$this->orm->persistAndFlush($importCache);
	}

	private function assignProperty(IEntity &$entity, array $values): void
	{
		foreach ($values as $key => $value) {
			$entity->{$key} = $value;
		}
	}

	private function createPrice(float|Money $price): Price
	{
		if (is_float($price)) {
			$price = Money::of($price, $this->order->currency);
		}
		return Price::from($price);
	}

	private function createVatRate(float|int|string|BigDecimal $vatRateValue): VatRate
	{
		return $this->order->country->vatRates->from($vatRateValue) ?? VatRate::Standard;
	}

	private function importInvoices(ImportCache $importCache): void
	{
		$this->order->setMetadata(['invoices' => $importCache->data->invoices]);
	}

}
