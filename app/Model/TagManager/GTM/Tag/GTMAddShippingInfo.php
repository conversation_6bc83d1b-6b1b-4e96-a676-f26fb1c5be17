<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\AddShippingInfo;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMAddShippingInfo implements RenderableTag
{

	public function __construct(
		private AddShippingInfo $addShippingInfo
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'add_shipping_info';
	}

	public function getData(): array
	{
		$items = [];

		$mutation = $this->addShippingInfo->shoppingCart->getMutation();
		$currency = $this->addShippingInfo->shoppingCart->getCurrency();

		$i = 0;
		/** @var ProductItem $productItem */
		foreach ($this->addShippingInfo->shoppingCart->getProducts() as $productItem) {
			$items[] = $productItem->variant->product->getLocalization($mutation)->getGTMData(
				currency: $currency,
				index: $i,
				quantity: $productItem->amount,
			);
			$i++;
		}

		$ecommerce = [
			'currency' => $this->addShippingInfo->shoppingCart->getCurrency()->getCurrencyCode(),
			'value' => $this->addShippingInfo->shoppingCart->getTotalPrice()->getAmount()->toFloat(),
			'items' => $items,
		];

		$coupon = $this->addShippingInfo->shoppingCart->getAppliedVoucherItem();
		if ($coupon !== null) {
			$ecommerce['coupon'] = $coupon->voucherCode->code;
		}

		$delivery = $this->addShippingInfo->shoppingCart->getDelivery();
		if ($delivery !== null) {
			$ecommerce['shipping_tier'] = $delivery->getName();
		}

		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
