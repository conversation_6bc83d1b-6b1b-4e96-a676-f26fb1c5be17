<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach;

use App\Event\AddToCart;
use App\Event\AfterLogin;
use App\Event\AfterLogout;
use App\Event\CategoryView;
use App\Event\NewsletterSubscribe;
use App\Event\OrderSubmit;
use App\Event\PageView;
use App\Event\ProductDetailView;
use App\Event\RemoveFromCart;
use App\Event\SearchResuls;
use App\Event\UserUpdate;
use App\Event\ViewCart;
use App\Exponea\Client;
use App\Exponea\Customer\CustomerIdInterface;
use App\Exponea\Customer\CustomerNewsletter;
use App\Exponea\Tracking\Event\CartUpdate;
use App\Exponea\Tracking\Event\Consent;
use App\Exponea\Tracking\Event\Purchase;
use App\Exponea\Tracking\Event\PurchaseItem;
use App\Model\Orm\Orm;
use App\Model\Security\User;
use App\Model\TagManager\Bloomreach\Tag\Checkout;
use App\Model\TagManager\Bloomreach\Tag\Search;
use App\Model\TagManager\Bloomreach\Tag\UserIdentify;
use App\Model\TagManager\Bloomreach\Tag\UserLogin;
use App\Model\TagManager\Bloomreach\Tag\UserLogout;
use App\Model\TagManager\Bloomreach\Tag\ViewCategory;
use App\Model\TagManager\Bloomreach\Tag\ViewItem;
use App\Model\TagManager\TagManager;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Strings;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class BloomreachEventSubscriber implements EventSubscriberInterface
{

	public function __construct(
		private TagManager $tagManager,
		private Request $httpRequest,
		private Response $httpResponse,
		private Orm $orm,
		private User $userSecurity,
		private Client $exponeaClient,
		private CustomerIdInterface $exponeaCustomerId,
	)
	{
	}
	public function onViewCart(ViewCart $event): void
	{
		$this->tagManager->add(new Checkout($event));
	}
	public function onProductDetailView(ProductDetailView $productDetailView): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new ViewItem($productDetailView, $this->userSecurity));
		}
	}

	public function onCategoryView(CategoryView $categoryView): void
	{
		$this->tagManager->add(new ViewCategory($categoryView, $this->userSecurity));
	}

	public function onLoggedIn(AfterLogin $event): void
	{
		$this->httpResponse->deleteCookie('loginComplete');
		$this->tagManager->add(new UserLogin($event));
	}

	public function onLoggedOut(AfterLogout $event): void
	{
		$this->httpResponse->deleteCookie('logoutComplete');
		$this->tagManager->add(new UserLogout($event));
	}

	public function onSearch(SearchResuls $event): void
	{
		$this->tagManager->add(new Search($event));
	}

	public function onPageView(PageView $pageView): void
	{
		if ($pageView->user !== null && !$this->httpRequest->isAjax()) {
			$this->tagManager->add(new UserIdentify($pageView));
		}
	}

	public function onUpdateCart(AddToCart|RemoveFromCart $event): void
	{
		try {
			$exponeaEvent = new CartUpdate($this->exponeaCustomerId, $event);
			bdump($this->exponeaClient->tracking()->addEvent($exponeaEvent)->wait());
		} catch (\Throwable $exception) {
			bdump($exception->getMessage());
			// do nothing
		}
	}

	public function onOrderSubmit(OrderSubmit $event): void
	{
		try {
			$exponeaEvent = new Purchase($this->exponeaCustomerId, $event);
			$this->exponeaClient->tracking()->queueEvent($exponeaEvent);
			foreach ($event->order->getProducts() as $productItem) {
				$exponeaEvent = new PurchaseItem($this->exponeaCustomerId, $event, $productItem, $this->userSecurity);
				$this->exponeaClient->tracking()->queueEvent($exponeaEvent);
			}

			$this->exponeaClient->tracking()->processQueue()->wait();

		} catch (\Throwable $exception) {
			// do nothing
		}
	}

	public function onUserUpdate(UserUpdate $event): void
	{
		try {
			$state = $this->orm->state->getById($event->customAddress['invState']);

			$data = [
				'first_name' => $event->customAddress['invFirstname'],
				'last_name' => $event->customAddress['invLastname'],
				'country' => $state->name,
				'city' => $event->customAddress['invCity'],
				'registration_date' => $event->user?->createdTime?->getTimestamp(),
				'phone' => $event->customAddress['invPhone'],
				'street' => $event->customAddress['invStreet'],
				'postal_code' => $event->customAddress['invZip'],
				'email' => $event->email,
				'language' => $state->isoCode,
				'locale' => Strings::lower($state->code),
			];

			$this->exponeaClient->tracking()->updateCustomerProperties($this->exponeaCustomerId, $data)->wait();
		} catch (\Throwable $exception) {
			// do nothing
		}
	}

	public function onNewsletterSubscribe(NewsletterSubscribe $event): void
	{
		try {
			$exponeaEvent = new Consent(new CustomerNewsletter($event->email), $event);
			$this->exponeaClient->tracking()->addEvent($exponeaEvent)->wait();
		} catch (\Throwable $exception) {
			// do nothing
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			ViewCart::class          => [
				['onViewCart', 0],
			],
			ProductDetailView::class => [
				['onProductDetailView', 0],
			],
			CategoryView::class      => [
				['onCategoryView', 0],
			],
			AfterLogin::class      => [
				['onLoggedIn', 0],
			],
			AfterLogout::class      => [
				['onLoggedOut', 0],
			],
			SearchResuls::class            => [
				['onSearch', 0],
			],
			PageView::class => [
				['onPageView', 0],
			],
			AddToCart::class => [
				['onUpdateCart', 0],
			],
			RemoveFromCart::class => [
				['onUpdateCart', 0],
			],
			OrderSubmit::class => [
				['onOrderSubmit', 0],
			],
			UserUpdate::class => [
				['onUserUpdate', 0],
			],
			NewsletterSubscribe::class => [
				['onNewsletterSubscribe', 0],
			],
		];
	}

}
