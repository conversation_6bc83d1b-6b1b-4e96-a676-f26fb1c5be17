<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Model\Orm\PriceLevel\PriceLevel;

final class DirectiveAction extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		if (!$action = $this->getAction()) {
			return 0.0;
		}

		if ($action >= 75) {
			return 5.0;
		}

		if ($action >= 50) {
			return 4.0;
		}

		if ($action >= 31) {
			return 3.0;
		}

		if ($action >= 21) {
			return 2.0;
		}

		if ($action >= 11) {
			return 1.0;
		}

		return 0.0;
	}

	private function getAction(): float
	{
		$priceSelling = $this->getPrice();
		$priceRecommended = $this->getPrice(PriceLevel::TYPE_RECOMMENDED);

		if (!$priceSelling) {
			return 0.0;
		}

		return ((1 - ($priceRecommended / $priceSelling)) * 100) * -1;
	}

	public function getName(): string
	{
		return 'Akce';
	}

}
