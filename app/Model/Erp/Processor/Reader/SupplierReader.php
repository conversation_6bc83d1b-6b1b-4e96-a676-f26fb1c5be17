<?php declare(strict_types=1);

namespace App\Model\Erp\Processor\Reader;

use App\Model\Erp\Exception\SkippedException;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Supplier\Model\Orm\Supplier\Supplier;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Psr\Log\LoggerInterface;

final class SupplierReader implements Reader
{

	private Mutation $defaultMutation;

	public function __construct(
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	public function init(): void
	{
		$this->orm->setPublicOnly(false);

		$this->defaultMutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->defaultMutation);

		$this->orm->setMutation($this->defaultMutation);
	}

	public function read(ImportCache $importCache, LoggerInterface $logger): void
	{
		assert($importCache->type === ImportCache::TYPE_SUPPLIER);

		$importing = false;
		$supplier = $this->getSupplier($importCache, $this->defaultMutation, $importing);

		if ($this->orm->importCache->findBy(['type' => $importCache->type, 'status' => [ImportCache::STATUS_READY], 'extId' => $importCache->extId])->countStored() > 0) {
			throw new SkippedException('Newer import pending.');
		}

		if ($supplier->syncChecksum === $this->createChecksum($importCache->data)) {
			throw new SkippedException('Checksum is same.');
		}

		$supplier->internalName = $importCache->data->name;
		$supplier->syncChecksum = $this->createChecksum($importCache->data);
		$supplier->syncTime = new DateTimeImmutable();

		$this->orm->persistAndFlush($supplier);
	}

	private function getSupplier(ImportCache $importCache, Mutation $mutation, bool &$importing): Supplier
	{
		if (($supplier = $this->orm->supplier->getByErpId((int) $importCache->data->id)) === null) {
			$supplier = new Supplier();
			$supplier->erpId = $importCache->data->id;
		}

		return $supplier;
	}

	private function createChecksum(ArrayHash $data): string
	{
		return md5(Json::encode($data));
	}

}
