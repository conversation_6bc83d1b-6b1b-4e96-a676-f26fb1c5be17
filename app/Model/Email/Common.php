<?php declare(strict_types = 1);

namespace App\Model\Email;

use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\EmailTemplate\EmailTemplateModel;
use App\Model\Mailer\BaseMailer;
use stdClass;
use App\Model\Mutation\MutationHolder;
use Throwable;

final class Common extends Base
{

	public function __construct(
		private readonly BaseMailer $baseMailer,
		MutationHolder $mutationHolder,
		EmailTemplateModel $emailTemplateModel,
		ImageObjectFactory $imageObjectFactory,
		EmailTemplateFactory $templateFactory,
	)
	{
		parent::__construct(
			$mutationHolder,
			$emailTemplateModel,
			$imageObjectFactory,
			$templateFactory,
		);
	}

	/**
	 * @param string|array|null $from
	 * @param string|array|null $to
	 * @throws Throwable
	 * @throws LogicException
	 */
	public function send($from, $to, string $dbTemplate, array $data, ?string $latteTemplate = null, array $replyTo = []): void
	{
		bdump($data);
		//1. render html from latte template and db_email_html
		$html = $this->render($dbTemplate, $latteTemplate);

		//2. replace [DATA-*]
		$html = $this->replaceDataArray($html, $data);
		//3. replace [XXXXXX]
		$html = $this->replaceDataTemplate($html, $data);

		$subject = $this->generateSubject($dbTemplate);
		$subject = $this->replaceDataArray($subject, $data);

		//$subject = $this->configService->get('projectName') . ' - ' . $subject;

		$data = new stdClass();
		$data->attachments = $this->getDataAttachment($dbTemplate);

		//3. send email
		$this->baseMailer->send($from, $to, $subject, $html, $data, $replyTo);
	}

	public function replaceDataTemplate(string $html, array $data): string
	{

		if (str_contains($html, '[RECAPITULATION]')) {
			$template = $this->templateFactory->createTemplate(FE_TEMPLATE_DIR . '/email/part/orderItemsList.latte');
			$template->order = $data["order"];
			$snippet = (string)$template;
			return str_replace('[RECAPITULATION]', $snippet, $html);
		}

		return $html;
	}

}
