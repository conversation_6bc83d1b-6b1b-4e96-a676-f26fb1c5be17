<?php declare(strict_types=1);

namespace App\Model\Sitemap\DataProvider;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Writer\Model\Orm\WriterLocalization\WriterLocalization;
use Closure;
use Elastica\QueryBuilder;
use Generator;

class WriterProvider extends ElasticCommonProvider implements YieldByMutation
{

	/**
	 * @return Closure(int $limit, int $offset): Generator
	 */
	public function getYieldFunction(Mutation $mutation): Closure
	{
		$b = new QueryBuilder();
		$filter = $b->query()->bool();

		$must = $this->esCommonRepository->getBaseMust(WriterLocalization::class);
		$must[] = $b->query()->exists('url');

		foreach ($must as $item) {
			$filter->addMust($item);
		}

		return function (int $limit, int $offset) use ($mutation, $filter) {
			foreach ($this->getBaseActivePostTypeYield($mutation, $filter, $limit, $offset) as $item) {
				$item['url'] = $mutation->getBaseUrl() . $item['url'];
				$item['changefreq'] = 'monthly';
				$item['priority'] = 0.3;
				yield $item;
			}
		};
	}

}
