<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\String;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Cache\CacheFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\String\StringEntity;
use App\Model\Orm\String\StringModel;
use App\Model\TranslatorDBCacheService;
use League\Csv\Reader;
use Nette\Application\AbortException;
use Nette\Application\BadRequestException;
use Nette\Application\UI\Form;
use Nette\Caching\Cache;
use Nette\Forms\Container;
use Nette\Http\FileUpload;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Throwable;
use Tracy\Debugger;
use Tracy\ILogger;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\DataGrid;

final class StringPresenter extends BasePresenter
{

	public const STRING_DATASOURCE_CACHE = 'stringsDataSource';

	private Cache $cache;

	private SessionSection $filterSession;

	/** @var array[][]  */
	protected array $strings;

	/** @var Mutation[]  */
	protected array $langs;

	public function __construct(
		private readonly CacheFactory $cacheFactory,
		private readonly StringModel $stringModel,
		private readonly TranslatorDBCacheService $translatorDBCacheService,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
		$this->cache = $this->cacheFactory->create('translations');
		$this->filterSession = $this->session->getSection('stringFilter');
		if (!isset($this->filterSession->data)) {
			$this->filterSession->data = null;
		}
	}

	public function actionDefault(): void
	{
		$this->filterSession->defaults = new ArrayHash();
		$this->langs = $this->orm->mutation->findAll()->fetchPairs('langCode');
		$this->strings = $this->createStringGridDatasource();

		$this->template->langs = $this->langs;
		$this->template->filterSession = $this->filterSession;
	}


	protected function createStringGridDatasource(): array
	{
		if ($this->getHttpRequest()->getQuery('do') === 'stringGrid-edit') {
			return []; //inline edit, we don't need data source
		}

		if (($data = $this->cache->load(self::STRING_DATASOURCE_CACHE)) !== null) {
			return $data;
		}

		/** @var ICollection<StringEntity> $stringsCollection */
		$stringsCollection = $this->orm->string->findNotParams()->findBy(['hidden' => 0]);

		$strings = [];
		foreach ($stringsCollection as $s) {
			$strings[$s->name]['id'] = $s->name;
			$strings[$s->name]['name'] = $s->name;
			$strings[$s->name][$s->lg] = $s->value;
		}

		foreach ($strings as &$string) {
			foreach ($this->langs as $lang) {
				if (!isset($string[$lang->langCode])) {
					$string[$lang->langCode] = '';
				}
			}
		}

		$res = array_values($strings);
		$this->cache->save(self::STRING_DATASOURCE_CACHE, $res, [Cache::Expire => '30 minutes']);

		return $res;
	}


	protected function createComponentStringGrid(): DataGrid
	{
		$grid = new DataGrid(null, 'stringGrid');
		$grid->setTemplateFile(__DIR__ . '/templates/datagrid-extend.latte');
		$grid->setDataSource($this->createStringGridDatasource());
		$grid->setPagination(true)->setDefaultPerPage($this->configService->getParam('adminPaging'));
		$grid->setTranslator($this->translator);
		$grid->setColumnsHideable();

		$grid->addColumnText('name', 'keyName')
			->setFilterText();

		foreach ($this->langs as $lang) {
			$col = $grid->addColumnText($lang->langCode, $lang->langCode);
			$col->setFilterText();
			$col->setTemplateEscaping(false);
			$col->setRenderer(function (array $item) use ($lang): string {
				return htmlspecialchars($item[$lang->langCode]);
			});
			$col->setEditableCallback(function (string $id, string $value) use ($lang): string {
				$entity = $this->orm->string->getBy(['name' => $id, 'lg' => $lang->langCode]);
				$this->orm->string->save($entity, ['value' => trim($value), 'name' => $id, 'lg' => $lang->langCode]);
				$this->orm->flush();
				$this->translatorDBCacheService->cleanCacheByKey($id);
				return  htmlspecialchars($value);
			});
		}

		//inline add action
		$add = $grid->addInlineAdd();
		$add->setPositionTop();

		$add->onControlAdd[] = function (Container $container) {
			$container->addText('name', '');
			//the following validator cannot be used, because group actions trigger error - the inlideAdd form is also a part of the group action form
			//->setRequired($this->translator->translate('msg_string_empty'));
			foreach ($this->langs as $lang) {
				$container->addText($lang->langCode, $lang->langCode);
			}
		};

		$add->onSubmit[] = function (ArrayHash $values) use ($grid): void {
			$name = trim($values->name);
			if ($name === '') {
				$this->flashMessage('msg_string_empty', 'error');
				$this->redrawControl('flashes');
				return;
			}

			if ($this->orm->string->getBy(['name' => $name]) !== null) {
				$this->flashMessage('msg_string_duplicate', 'error');
				$this->redrawControl('flashes');
				return;
			}

			foreach ($values as $key => $val) {
				if ($key === 'name') {
					continue;
				}

				$val = trim($val);
				if ($val === '') {
					$val = '##' . $name;
				}

				$this->orm->string->save(null, ['name' => $name, 'lg' => $key, 'value' => trim($val)]);
			}

			$this->orm->flush();
			$this->translatorDBCacheService->cleanCacheByKey($name);
			$grid->setDataSource($this->createStringGridDatasource());

			$this->flashMessage('msg_ok_saved', 'ok');
			$this->redrawControl('flashes');
		};

		//delete action
		$grid->addAction('delete', 'delete_button', 'delete!')
			->setClass('btn btn-xs btn-danger')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return $this->translator->translate('delete_confirm') . ' [' . $item['name'] . ']';
					}
				)
			);

		//export actions
		$grid->addExportCallback('export_all', function (array $data, DataGrid $grid) {
			$this->export($data, array_keys($this->langs));
		}, false);

		$grid->addExportCallback('export_filtered', function (array $data, DataGrid $grid) {
			$grid->getColumns(); //must be called before getColumnsVisibility() in order to get visibility right
			$visibility = $grid->getColumnsVisibility();
			$langs = array_keys($this->langs);
			$langs = array_filter($langs, function (string $lang) use ($visibility) {
				return isset($visibility[$lang]) && $visibility[$lang]['visible'];
			});

			$this->export($data, $langs);
		}, true);

		//group delete action
		if ($this->user->isDeveloper()) {
			$groupDelete = $grid->addGroupButtonAction('delete_button');
			$groupDelete->setClass('btn btn-xs btn-danger ajax group-delete-button');
			$groupDelete->setAttribute('data-datagrid-confirm', $this->translator->translate('delete_confirms'));
			$groupDelete->onClick[] = [$this, 'groupDelete'];
		}

		return $grid;
	}

	protected function export(array $data, array $langs): void
	{
		try {
			$file = $this->stringModel->export($data, $langs);
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
			Debugger::log($e);
			return;
		}

		$reader = Reader::createFromPath($file);
		$reader->download('translations.csv'); //this is used instead of sending FileResponse, so that the $file can be deleted after that
		unlink($file);
		exit;
	}

	public function handleDelete(string $id): void
	{
	 	$this->orm->string->findBy(['name' => $id])->fetchPairs(null, 'id');
		$this->translatorDBCacheService->cleanCacheByKey($id);
		$this->orm->string->delete($id);
		$this->orm->flush();

		$this['stringGrid']->setDataSource($this->createStringGridDatasource());

		$this->flashMessage('msg_ok_deleted', 'ok');

		if ($this->isAjax()) {
			$this->redrawControl('flashes');
			$this['stringGrid']->reload();
		} else {
			$this->redirect('this');
		}
	}

	/**
	 * @throws BadRequestException
	 * @throws AbortException
	 */
	public function groupDelete(array $items): void
	{
		if (!$this->user->isDeveloper()) {
			$this->error('Only developers can run this action. Resistance is futile!');
		}

		$names = [];
		foreach ($items as $item) {
			$names = $names + $this->orm->string->findBy(['name' => (string) $item])->fetchPairs(null, 'name');
			$this->orm->string->delete((string) $item);
		}

		$this->orm->flush();
		foreach ($names as $name) {
			$this->translatorDBCacheService->cleanCacheByKey($name);
		}
		$this['stringGrid']->setDataSource($this->createStringGridDatasource());

		if ($this->isAjax()) {
			$this->redrawControl('flashes');
			$this['stringGrid']->setDataSource($this->createStringGridDatasource());
			$this['stringGrid']->reload();
		} else {
			$this->redirect('this');
		}
	}

	protected function createComponentImportForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addUpload('file', 'file');
		$form->addCheckbox('updateOnly', 'update_only')
			->setDefaultValue(true);
		$form->addSubmit('import', $this->translator->translate('import_button'));

		$form->onSuccess[] = [$this, 'importFormSucceeded'];
		return $form;
	}

	/**
	 * @throws AbortException
	 */
	public function importFormSucceeded(Form $form, ArrayHash $values): void
	{
		try {
			/** @var FileUpload $upload */
			$upload = $values->file;
			if ($upload->ok) {
				$report = $this->stringModel->import($upload->temporaryFile, (bool) $values->updateOnly);
				$message = $this->translator->translate('updated') . ': ' . $report['total']['updated'] . ', ' .
					$this->translator->translate('inserted') . ': ' . $report['total']['inserted'] . ', ' .
					$this->translator->translate('skipped') . ': ' . $report['total']['skipped'];
					$this->flashMessage($message, 'error');
				unlink($upload->temporaryFile);
			}
		} catch (Throwable $e) {
			Debugger::log($e, ILogger::ERROR);
			$this->flashMessage('msg_operation_failed', 'error');
		}
		$this->translatorDBCacheService->cleanCache();

		$this->redirect('this');
	}

}
