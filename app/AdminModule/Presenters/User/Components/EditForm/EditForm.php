<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User\Components\EditForm;

use App\FrontModule\Components\CartUserDetail\CartUserDetail;
use App\Model\Image\ImageObjectFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use Exception;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use App\Model\Translator;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Throwable;
use Tracy\Debugger;

/**
 * @property-read DefaultTemplate $template
 */
final class EditForm extends UI\Control
{

	private bool $isEdit;

	public function __construct(
		private User $user,
		private array $mutations,
		private readonly User $userEntity,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly UserModel $userModel,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly MutationHolder $mutationHolder,
	)
	{
		$this->isEdit = isset($this->user->id);
	}


	public function render(): void
	{
		$this->template->object = $this->user;
		$this->template->customAddress = $this->user->customAddress;

		$this->getTemplate()->lastOrder = $this->orm->order->findBy(['user' => $this->user, 'state!=' => OrderState::Draft])->orderBy('id', ICollection::DESC)->fetch();

		$this->template->fileUploadLink = $this->presenter->link('File:upload');
		$this->template->userEntity = $this->userEntity;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->isEdit = $this->isEdit;
		$this->template->states = $this->user->mutation->states->toCollection()->fetchPairs('id', 'name');

		$this->template->priceLevel = $this->orm->priceLevel->getDefault();
		$this->template->stateCallback = fn(Mutation $mutation) =>  $this->orm->state->getDefault($mutation);
		$orderedProductLocalizationIds = $this->userModel->findOrderedProductLocalizationIds($this->user->id, $this->user->mutation);
		$this->template->lastVisited = $this->user->productVisits->toCollection()->findBy(['visitedAt>=' => new DateTimeImmutable()->modify('-1 month'), 'productLocalization->id!='=>$orderedProductLocalizationIds])->orderBy('visitedAt', ICollection::DESC)->limitBy(100);

		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/editForm.latte');
	}

	protected function createComponentEditForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'email')->setRequired('email_required');
		$form->addPassword('password', 'password');
		$form->addPassword('passwordVerify', 'password_verify');

		$form['password']
			->setRequired(false)
			->addRule(function ($field, $form) {
				return $form['passwordVerify']->value === $field->value;
			}, 'msg_password_not_same', $form);

		$form['passwordVerify']
			->setRequired(false)
			->addRule(function ($field, $form) {
				return $form['password']->value === $field->value;
			}, 'msg_password_not_same', $form);

		if (!$this->isEdit) {
			$form['password']->setRequired('set_password');
		}

		$states = $this->isEdit ? $this->user->mutation->states->toCollection()->fetchPairs('id', 'name') : $this->orm->state->findAll()->fetchPairs('id', 'name');
		$priceLevels = $this->orm->priceLevel->findAll()->fetchPairs('id', 'name');

		$user = $this->presenter->user;
		assert($user instanceof \App\Model\Security\User);
		$form->addSelect('role', 'role', $user->getAllowedRoles());
		$form->addSelect('priceLevel', 'price_level_title', $priceLevels);
		$form->addText('firstname', 'user_firstname');
		$form->addText('lastname', 'user_lastname');
		$form->addText('phone', 'phone');
		$form->addText('street', 'street');
		$form->addText('city', 'city');
		$form->addText('zip', 'zip');
		$form->addSelect('state', 'state', $states);//->setPrompt('select_prompt_states');
		$form->addText('ic', 'company_id');
		$form->addText('dic', 'vat_number');
		$form->addText('company', 'company');
		$form->addCheckbox('freeTransit', 'free_transit');
		$form->addCheckbox('payWithInvoice', 'pay_with_invoice');
		$form->addCheckbox('isClubMember', 'is_club_member')
			->setDisabled();

		if ($this->isEdit) {
			$values = [];
			$form->addHidden('userMutations', $this->user->mutation->id);
			$values['email'] = $this->user->email;
			$values['role'] = $this->user->role;
			$values['priceLevel'] = $this->user->priceLevel->id;
			$values['firstname'] = $this->user->firstname;
			$values['lastname'] = $this->user->lastname;
			$values['phone'] = $this->user->phone;
			$values['street'] = $this->user->street;
			$values['city'] = $this->user->city;
			$values['zip'] = $this->user->zip;
			$values['state'] = $this->user->state;
			$values['ic'] = $this->user->ic;
			$values['dic'] = $this->user->dic;
			$values['company'] = $this->user->company;
			$values['freeTransit'] = $this->user->freeTransit;
			$values['payWithInvoice'] = $this->user->payWithInvoice;
			$values['userMutations'] = $this->user->mutation ? $this->user->mutation->id : null; // verze s 1 mutaci per User
			$values['state'] = $this->user->state && isset($states[$this->user->state->id]) ? $this->user->state->id : null;

			$form->setDefaults($values);
		} else {
			$form->addSelect('userMutations', 'label_mutation', $this->mutations);
		}

		$this->buildAdresses($form);
		$form->addSubmit('save', 'Save');

		$form->onError[] = [$this, 'editFormError'];
		$form->onValidate[] = [$this, 'editFormValidate'];
		$form->onSuccess[] = [$this, 'editFormSucceeded'];

		return $form;
	}

	private function buildAdresses(Form $form): void
	{
		$states = $this->mutationHolder->getMutation()->states->toCollection()->fetchPairs('id', 'name');
		$phonePrefixBaseCountry = $this->mutationHolder->getMutation()->states->toCollection()->fetch()->code;

		if ($this->user->customAddress) {
			foreach ($this->user->customAddress as $k => $i) {
				// fakturacni
				$form->addText('inv_firstname_' . $k, 'form_label_firstname')
					->setDefaultValue($i->invFirstname);
				$form->addText('inv_lastname_' . $k, 'form_label_lastname')
					->setDefaultValue($i->invLastname);
				$form->addText('inv_phone_' . $k, 'form_label_phone')
					->setDefaultValue($i->invPhone)
					->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);
				$form->addText('inv_street_' . $k, 'form_label_street')
					->setDefaultValue($i->invStreet ?? '');
				$form->addText('inv_city_' . $k, 'form_label_city')
					->setDefaultValue($i->invCity ?? '');
				$form->addText('inv_zip_' . $k, 'form_label_zip')
					->setDefaultValue($i->invZip ?? '');
				$form->addSelect('inv_state_' . $k, 'form_label_state', $states);

				if (isset($i->invState, $states[$i->invState])) {
					$form['inv_state_' . $k]->setDefaultValue($i->invState);
				}

				// firma
				$form->addText('inv_company_' . $k, 'form_label_company')
					->setDefaultValue($i->invCompany ?? '');
				$form->addText('inv_ic_' . $k, 'form_label_ic')->setDefaultValue($i->invIc ?? '');
				$form->addText('inv_dic_' . $k, 'form_label_dic')
					->setDefaultValue($i->invDic ?? '');

				// dodaci
				$form->addHidden('deliveryTab_' . $k);
				if (!empty($i->delCity)) {
					$form['deliveryTab_' . $k]->setDefaultValue(true);
				}
				$form->addText('del_firstname_' . $k, 'form_label_firstname')->setDefaultValue($i->delFirstname ?? '');
				$form->addText('del_lastname_' . $k, 'form_label_lastname')->setDefaultValue($i->delLastname ?? '');
				$form->addText('del_phone_' . $k, 'form_label_phone');
				$form->addText('del_street_' . $k, 'form_label_street')->setDefaultValue($i->delStreet ?? '');
				$form->addText('del_city_' . $k, 'form_label_city')->setDefaultValue($i->delCity ?? '');
				$form->addText('del_zip_' . $k, 'form_label_zip')->setDefaultValue($i->delZip ?? '');
				$form->addSelect('del_state_' . $k, 'form_label_state', $states);

				if (isset($i->delState, $states[$i->delState])) {
					$form['del_state_' . $k]->setDefaultValue($i->delState ?? '');
				}

				$form->addText('del_company_' . $k, 'form_label_company')->setDefaultValue($i->delCompany ?? '');
			}
		}
	}

	public function editFormError(UI\Form $form): void
	{
		$this->redrawControl();
	}

	public function editFormValidate(UI\Form $form, ArrayHash $values): void
	{
		if (!$this->isEdit || $this->user->email !== $values->email) { // pri add nebo zmene emailu
			$mutation = $this->orm->mutation->getById($values['userMutations']);
			$user = $this->orm->user->getByEmail($values->email, $mutation);

			if ($user) {
				$form['email']->addError('mail_exist');
				$form->addError('mail_exist');
			}
		}

		if (!$this->isEdit && $values['state']) { // stat musi patrit zvolene mutaci
			$mutation = $this->orm->mutation->getById($values['userMutations']);

			if (!$mutation->states->toCollection()->getById($values['state'])) {
				$form['state']->addError('state_msg_error_unknown_for_mutation');
				$form->addError('state_msg_error_unknown_for_mutation');
			}
		}
	}

	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		assert(is_array($valuesAll));
		try {
			if (isset($this->user->id)) {
				$action = 'edit';
			} else {
				$action = 'create';
			}

			$newAddresses = [];
			if ($this->user->customAddress) {
				foreach ($this->user->customAddress as $k => $i) {
					$newAddress = CartUserDetail::getAddressLine($valuesAll, '_' . $k);
					$newAddress['last'] = $i->last ?? false;
					$newAddresses[] = $newAddress;
				}
			}

			$valuesAll['customAddress'] = $newAddresses;

			$this->userModel->save($this->user, $valuesAll, (int) $this->presenter->getUser()->getId(), $action);

			$this->flashMessage('OK', 'ok');

			if ($action === 'edit') {
				if ( ! $this->presenter->isAjax()) {
					$this->presenter->redirect('this', $this->user->id);
				} else {
					$this->redrawControl();
				}
			} else {
				$this->presenter->redirect('edit', $this->user->id);
			}
		} catch (AbortException $e) {
			throw $e;
		} catch (Throwable $e) {
			$form->addError($e->getMessage());
		}
	}

	public function handleDeleteCustomAddress(int|null $key = null): void
	{
		try {
			if (isset($key, $this->user->customAddress)) {

				if ($this->user->customAddress) {
					$addresses = [];

					foreach ($this->user->customAddress as $k => $address) {
						if ($k == $key) {
							continue;
						}
						$addresses[] = $address;
					}
					$this->user->customAddress = $addresses;
					$this->orm->user->persistAndFlush($this->user);

					$this->flashMessage('form_profil_address_deleted_ok', 'ok');
				}
			}

		} catch (Exception $e) {
			Debugger::log($e, Debugger::EXCEPTION);
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}

	public function handleDelete(): never
	{
		$this->userModel->delete($this->user);
		$this->presenter->redirect('default');
	}

}
