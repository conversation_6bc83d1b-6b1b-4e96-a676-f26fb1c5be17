<?php declare(strict_types = 1);

namespace App\PostType\Theme\Model\Orm\ThemeLocalizationVisit;

use App\Model\Orm\BaseEntity;
use App\PostType\Theme\Model\Orm\ThemeLocalization\ThemeLocalization;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int                    $id                                                   {primary}
 * @property int                    $view                                                 {default 0}
 * @property DateTimeImmutable|null $visitDate                                            {default now}
 *
 * RELATIONS
 * @property ThemeLocalization      $themeLocalization                                    {M:1 ThemeLocalization::$visits}
 *
 * VIRTUAL
 */
class ThemeLocalizationVisit extends BaseEntity
{

}
