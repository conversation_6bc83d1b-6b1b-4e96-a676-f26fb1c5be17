{varType App\PostType\Blog\Model\Orm\BlogLocalization $object}



{block content}

	<div class="u-pt-lg">
		<div class="row-main">
			{control breadcrumb}

			{include $templates.'/part/box/annot.latte', date=>true}
			<p>{_'article_reading_time'}: {$object->readingTime} {_'minutes'}</p>{* vypoctena doba precteni podle obsahu *}

			{include $templates.'/part/crossroad/tags.latte', crossroad=>$object->blogTags, customTitle=>tag}

			{control attachedBlogs}

			{foreach $object->categories as $category}
				<div>
					<a n:href="$category">
						{$category->name}
					</a>
				</div>
			{/foreach}

			{foreach $object->authors as $author}
				<div>
					<a n:href="$author">
						{$author->name}
					</a>
				</div>
			{/foreach}

			{control customContentRenderer}

			{include $templates.'/part/crossroad/tags.latte', crossroad=>$tagsWithCount}
		</div>
	</div>


{/block}
