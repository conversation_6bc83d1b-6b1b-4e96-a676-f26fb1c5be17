<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<BlogLocalization>
 */
class BlogLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'blog_localization';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}


	/**
	 * @return ICollection<BlogLocalization>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<BlogLocalization>
	 */
	public function findByIdInPathString(CommonTree $commonTree): ICollection
	{
		$builder = $this->builder()
			->andWhere('pathString LIKE %_like_', '|' . $commonTree->id . '|');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<BlogLocalization>
	 */
	public function findFiltered(array $ids): ICollection
	{
		$builder = $this->builder()->select('p.*')->from($this->getTableName(), 'p')
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('bl.id')
			->from($this->getTableName(), 'bl')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit): Result
	{
		$builder = $this->builder()->select('bl.id')
			->from($this->getTableName(), 'bl')
			->andWhere('bl.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

}
