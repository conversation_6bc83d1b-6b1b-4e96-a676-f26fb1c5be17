<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method BlogLocalization|null getById($id)
 * @method ICollection<BlogLocalization> searchByName(string $q, array $excluded = [])
 * @method ICollection<BlogLocalization> findByIdInPathString(CommonTree $commonTree)
 * @method ICollection<BlogLocalization> findFiltered(array $ids)
 * @method array findAllIds(?int $limit)
 *
 * @extends Repository<BlogLocalization>
 */
final class BlogLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [BlogLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];


		return $ret;
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof BlogLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
