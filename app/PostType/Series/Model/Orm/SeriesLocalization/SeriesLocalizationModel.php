<?php declare(strict_types=1);

namespace App\PostType\Series\Model\Orm\SeriesLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\PostType\Series\Model\Orm\Series\Series;
use Nette\Utils\Strings;

final readonly class SeriesLocalizationModel
{

	public function __construct(
		private SeriesLocalizationRepository $seriesLocalizationRepository,
	)
	{
	}

	public function create(Mutation $mutation, string $name, ?ParameterValue $parameterValue = null, bool $public = true, ?int $erpId = null, ?string $aliasPrefix = 'Série ', ?string $uid = null): SeriesLocalization
	{
		$localization = new SeriesLocalization();
		$localization->erpId = $erpId;
		$localization->mutation = $mutation;
		$localization->name = $name;
		$localization->nameTitle = $name;
		$localization->nameAnchor = $name;

		$localization->description = '';

		$localization->public = $public;
		$this->seriesLocalizationRepository->attach($localization);

		$localizableEntity = new Series();
		$localizableEntity->uid = $uid;
		$localizableEntity->parameterValueSeries = $parameterValue;
		$localizableEntity->internalName = Strings::substring($name, 0, 49);
		$localization->series = $localizableEntity;

		$this->seriesLocalizationRepository->persistAndFlush($localization);
		$localization->setAlias($aliasPrefix . $localization->name);

		return $localization;
	}

}
