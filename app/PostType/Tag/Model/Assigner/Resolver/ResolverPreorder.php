<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Assigner\Resolver;

use App\Model\ElasticSearch\Product\Convertor\FlagData;
use App\Model\Orm\Product\Product;
use App\PostType\Tag\Model\TagType;

final class ResolverPreorder extends Resolver
{

	protected bool $reflectTag = false;

	public function resolve(Product $product): bool
	{
		if ($product->hasTag($this->getTagType())) {
			return true;
		}

		$product->tags->add($this->tagRepository->getBy(['type' => TagType::preorder]));

		return true;
	}

	public function getEsProductConvertors(): array
	{
		return [FlagData::class];
	}

}
