application:
	mapping:
		ProductReview: App\PostType\ProductReview\*Module\Presenters\*Presenter

parameters:
	config:
		review:
				paging:
					productReviewUser: 3
					productReviewFirst: 2
					productReviewOther: 10
					minProductDetailReviews: 12
					productReviewDetailFirst: 2
					productReviewDetailOther: 10
	postTypeRoutes:
		ProductReview: product-review


cf:
	fields:
		reviewRating:
			type: list
			items:
				review:
					type: text
					multiple: true
		content:
			type: group
			label: 'Uživatelská recenze'
			items:
				reviewRatingPositive:
					label: Klady
					extends: @cf.reviewRating
				reviewRatingNegative:
					label: Zápory
					extends: @cf.reviewRating

	templates:
		productReview: [@cf.content]

services:
	- App\PostType\ProductReview\AdminModule\Components\ProductReviewList\ProductReviewListFactory
	- App\PostType\ProductReview\AdminModule\Components\ProductReviewForm\ProductReviewFormFactory
	- App\PostType\ProductReview\FrontModule\Components\ProductReviews\ProductReviewsFactory
	- App\PostType\ProductReview\FrontModule\Components\Review\ReviewFactory
	- App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd\ProductReviewAddFactory
	- App\PostType\ProductReview\FrontModule\Components\ProductDetailReviews\ProductDetailReviewsFactory
	- App\PostType\ProductReview\FrontModule\Components\UserProductReviews\UserProductReviewsFactory
