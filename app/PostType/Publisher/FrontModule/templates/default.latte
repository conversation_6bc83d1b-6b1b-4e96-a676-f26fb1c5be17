{varType App\PostType\Publisher\Model\Orm\PublisherLocalization\PublisherLocalization $object}
{block content}
	<div class="row-main">
		{control breadcrumb}

		<div class="u-mb-last-0 u-mb-md u-mb-2xl@md">
			{if $letter}
				{capture $letterDetailTitle}{$object->name} {_"letter_from"} {$letter}{/capture}
				{include $templates.'/part/box/annot.latte', class=>'u-mb-xxs u-mb-xs@md', name=>$letterDetailTitle->__toString()}
			{else}
				{include $templates.'/part/box/annot.latte', class: 'u-mb-xxs u-mb-xs@md'}
			{/if}
			{include $templates.'/part/crossroad/std.latte'}
			{include $templates.'/part/box/content.latte'}

			{control publisherList}
		</div>
	</div>
{/block}
