<?php declare(strict_types = 1);

namespace App\PostType\Publisher\Model\Orm\Publisher;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Publisher\Model\Orm\PublisherLocalization\PublisherLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;// phpcs:ignore

/**
 * @property int                                $id                         {primary}
 * @property string                             $internalName               {default ''}
 * @property ArrayHash                          $customFieldsJson           {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<PublisherLocalization> $localizations              {1:M PublisherLocalization::$publisher}
 * @property ParameterValue|null                $parameterValuePublisher    {1:1 ParameterValue::$publisher, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null                     $cf                         {virtual}
 */
class Publisher extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<PublisherLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): PublisherLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof PublisherLocalization);
		return $localization;
	}

}
